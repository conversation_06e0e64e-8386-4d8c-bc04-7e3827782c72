# 家政人广场到家订单完整抢单处理流程分析

## 📋 流程概述

家政人广场到家订单抢单流程是一个完整的业务闭环，从需求发布到服务完成，涉及多个角色和系统模块的协同工作。

## 🔄 完整流程图

```mermaid
graph TD
    A[需求广场展示待抢单订单] --> B[门店用户点击抢单]
    B --> C[确认抢单弹窗]
    C --> D[执行抢单API调用]
    D --> E{抢单结果判断}
    E -->|成功| F{订单类型判断}
    E -->|失败| G[显示失败提示]
    F -->|普通订单| H[直接跳转派单页面]
    F -->|共享订单| I[选择服务员工]
    I --> J[员工选择成功]
    J --> H
    H --> K[管理员进行派单]
    K --> L[员工收到派单通知]
    L --> M[员工确认接单]
    M --> N[订单进入服务流程]
```

## 🎯 详细流程步骤

### 1. 需求展示阶段

**位置**: `storeui/src/pages/demand-square/index.vue`

**条件**:
- `business_type = 1` (到家订单)
- `demand_status = 1` (待抢单状态)
- 创建时间 < 30分钟 (有效期限制)

**展示内容**:
- 订单基本信息
- 服务地址
- 服务时间
- 价格信息
- 抢单按钮

### 2. 抢单触发阶段

**用户操作**: 点击"抢单"按钮

**前端处理**:
```javascript
// 抢单操作确认
grabOrder(order) {
  uni.showModal({
    title: '确认抢单',
    content: `确定要抢取这个到家订单吗？`,
    success: (res) => {
      if (res.confirm) {
        this.performGrabOrder(order);
      }
    }
  });
}
```

### 3. 抢单执行阶段

**API调用**: `/api/v1/demand-square/grab`

**请求参数**:
```json
{
  "demand_uuid": "需求UUID",
  "store_uuid": "门店UUID"
}
```

**前端处理流程**:
1. 显示"抢单中..."加载提示
2. 获取当前用户门店信息
3. 调用抢单API
4. 处理返回结果

### 4. 后端抢单处理逻辑

**文件位置**: `storeapi/module_admin/service/demand_square_service.py`

**核心处理步骤**:

#### 4.1 参数验证
- 验证 `demand_uuid` 不为空
- 验证 `store_uuid` 不为空
- 获取用户信息和门店信息

#### 4.2 业务规则验证
```python
# 验证需求状态
if demand_info.demand_status != 1:
    return {"success": False, "message": "该需求已被抢单或已失效"}

# 验证有效期（到家订单30分钟）
current_time = datetime.now()
expire_time = demand_info.create_time + timedelta(minutes=30)
if current_time > expire_time:
    return {"success": False, "message": "需求已过期，无法抢单"}
```

#### 4.3 原子性状态更新（乐观锁）
```python
# 使用乐观锁防止并发抢单
update_demand_query = update(DemandSquare).where(
    and_(
        DemandSquare.uuid == demand_uuid_str,
        DemandSquare.demand_status == 1  # 确保状态仍为待抢单
    )
).values(
    demand_status=2,  # 更新为已抢单
    grab_store_uuid=grab_data.store_uuid,
    grab_store_name=store_info._mapping.get("name"),
    grab_user_uuid=user_uuid,
    grab_user_name=user_name,
    grab_time=grab_time,
    update_time=grab_time
)

update_result = await db.execute(update_demand_query)

# 检查更新是否成功（并发控制）
if update_result.rowcount == 0:
    return DemandGrabResponseVO(
        success=False,
        message="抢单失败，该需求已被其他门店抢走"
    )
```

#### 4.4 创建订单记录
- 生成订单编号
- 创建订单基础信息
- 设置初始状态为 10（已接单）

### 5. 抢单成功后处理

#### 5.1 普通到家订单
```javascript
// 显示成功提示
uni.showToast({
  title: '抢单成功，订单已创建！',
  icon: 'success',
  duration: 2000
});

// 刷新列表并跳转到派单页面
setTimeout(() => {
  this.loadOrderList(true);
}, 1000);

setTimeout(() => {
  uni.switchTab({
    url: '/pages/dispatch/index'
  });
}, 2000);
```

#### 5.2 共享到家订单
```javascript
// 需要选择员工
if (response.need_staff_selection) {
  this.currentGrabbedOrder = order;
  this.loadAvailableStaff();
  // 显示员工选择弹窗
}
```

**员工选择API**: `/api/v1/demand-square/select-staff`

### 6. 派单管理阶段

**页面位置**: `storeui/src/pages/dispatch/index.vue`

**功能特点**:
- 显示已抢单的订单列表
- 订单状态为 10（已接单）
- 管理员可选择员工进行派单
- 派单后状态变为 20（派单待确认）

**派单操作**:
```javascript
// 跳转到员工选择页面
dispatchOrder(order) {
  uni.navigateTo({
    url: `/pages-dispatch/staff-select?orderNumber=${order.order_number}`
  });
}
```

### 7. 员工接单确认阶段

**页面位置**: `storeui/src/pages-staff/home/<USER>

**员工端处理**:
```javascript
// 员工确认接单
async grabOrder(order) {
  uni.showModal({
    title: '确认接单',
    content: `确定要接此单吗？\n订单号：${order.order_number}\n服务：${order.product_name}\n提成：¥${order.commission_amount || '0'}`,
    success: async res => {
      if (res.confirm) {
        const response = await acceptStaffOrder(order.order_number);
        if (response) {
          uni.showToast({
            title: '接单成功',
            icon: 'success',
          });
          // 跳转到待服务页面
          this.switchTab('waiting');
        }
      }
    }
  });
}
```

**后端接单API**: `/api/v1/staff-order/acceptOrder`

**后端处理逻辑**:
```python
# 验证员工权限
has_permission = await StaffOrderDao.verify_staff_order_permission(
    query_db, staff_id, order_number
)

# 验证订单状态必须为20（派单待确认）
current_status = await StaffOrderDao.get_order_status(query_db, order_number)
if current_status != 20:
    raise BusinessException(message="订单状态不正确，无法接单")

# 更新订单状态为40（已派单）
success = await StaffOrderDao.update_order_status(
    query_db, order_number, 40, "已派单"
)
```

## 📊 订单状态流转规则

| 状态码 | 状态名称 | 说明 | 触发条件 | 下一状态 |
|--------|----------|------|----------|----------|
| 10 | 已接单 | 抢单成功的初始状态 | 抢单成功 | 20 |
| 20 | 派单待确认 | 管理员已派单给员工 | 管理员派单 | 40/30 |
| 30 | 拒绝接单 | 员工拒绝接单 | 员工拒绝 | 终止 |
| 40 | 已派单 | 员工确认接单 | 员工确认 | 50 |
| 50 | 执行中 | 员工开始执行订单 | 员工操作 | 60 |
| 60 | 开始服务 | 员工到达现场开始服务 | 员工操作 | 80 |
| 80 | 已完成 | 服务完成 | 员工完成服务 | 90 |
| 90 | 已评价 | 客户评价完成 | 客户评价 | 结束 |
| 99 | 已取消 | 订单取消 | 各种取消情况 | 结束 |

**合法状态流转**:
```python
valid_transitions = {
    40: [50],  # 已派单 → 执行中
    50: [60],  # 执行中 → 开始服务
    60: [80],  # 开始服务 → 已完成
    70: [80],  # 服务结束 → 已完成（兼容旧流程）
}
```

## 🔒 安全机制与异常处理

### 并发控制
- **乐观锁机制**: 使用数据库行级锁防止多门店同时抢单
- **原子性操作**: 状态更新和订单创建在同一事务中
- **重复提交防护**: 前端loading状态防止重复点击

### 权限验证
- **员工权限**: 员工只能操作分配给自己的订单
- **门店权限**: 验证门店是否有效且未停用
- **状态权限**: 严格的状态流转规则验证

### 异常处理
```python
try:
    # 业务逻辑处理
    result = await process_grab_demand()
    await db.commit()
    return success_response
except BusinessException as e:
    await db.rollback()
    logger.error(f"业务异常: {e.message}")
    raise e
except Exception as e:
    await db.rollback()
    logger.error(f"系统异常: {str(e)}")
    raise BusinessException(message="系统异常，请重试")
```

### 超时机制
- **抢单有效期**: 到家订单30分钟内有效
- **派单超时**: 可配置派单超时自动回收机制
- **服务超时**: 可配置服务超时提醒机制

## 🚀 关键技术特点

1. **响应式设计**: 支持多端适配（小程序、H5）
2. **实时更新**: 订单状态实时同步
3. **消息推送**: 关键节点消息通知
4. **数据一致性**: 完整的事务控制
5. **性能优化**: 合理的缓存策略和数据库索引

## 📝 总结

家政人广场到家订单抢单流程是一个完整的业务闭环，通过严格的状态管理、权限控制和异常处理，确保了从需求发布到服务完成的全流程可控性和数据一致性。该流程设计充分考虑了并发场景、异常情况和用户体验，是一个成熟的商业化解决方案。
