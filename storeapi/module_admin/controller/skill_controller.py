"""
技能管理控制器
"""
from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_admin.service.auth_adapter import AuthAdapter
from module_admin.service.internal_user_login_service import InternalUserLoginService
from module_admin.service.staff_skill_service import StaffSkillService
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, BusinessException, ValidationException

# 使用统一的API路由前缀格式
skill_controller = APIRouter(prefix='/api/v1/skill', dependencies=[Depends(AuthAdapter.get_current_user)])

@skill_controller.get('/company-skills', summary="获取公司版本关联的技能选项")
async def get_company_skills(
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取当前公司版本关联的技能选项接口
    
    通过version_skill_relation表获取当前公司版本关联的技能ID，
    然后查询service_skill表获取技能昵称，用于新建产品时的技能选择
    
    Args:
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        技能选项列表数据
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        company_uuid = current_user.user.company_id
        
        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")
            
        if not company_uuid:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")
            
        logger.info(f"用户 {current_user_id} 获取公司技能选项，公司: {company_uuid}")
        
        # 调用服务层获取公司版本关联的技能
        from sqlalchemy import text
        
        # 1. 获取公司当前有效版本的UUID列表
        version_sql = """
            SELECT DISTINCT version_uuid
            FROM company_version_relation
            WHERE company_uuid = :company_uuid
            AND status = 1
            AND expire_time > NOW()
        """
        version_result = await query_db.execute(text(version_sql), {"company_uuid": company_uuid})
        version_rows = version_result.fetchall()
        
        if not version_rows:
            logger.warning(f"公司 {company_uuid} 没有有效的版本关联")
            return ResponseUtil.success(
                msg="获取技能选项成功",
                data=[]
            )
        
        version_uuids = [row.version_uuid for row in version_rows]
        logger.info(f"公司 {company_uuid} 的有效版本: {version_uuids}")
        
        # 2. 获取版本关联的技能
        skills = await StaffSkillService._get_skills_by_versions(query_db, version_uuids)
        
        # 3. 过滤出二级技能（用于产品分类）
        skill_options = []
        for skill in skills:
            # 只返回二级技能（level=2）作为产品分类选项
            if skill.level == 2:
                skill_options.append({
                    "id": skill.id,
                    "name": skill.name,
                    "parent_id": skill.parent_id,
                    "level": skill.level,
                    "sort_order": skill.sort_order
                })
        
        # 按sort_order排序
        skill_options.sort(key=lambda x: x.get('sort_order', 0))
        
        logger.info(f"获取公司技能选项成功，共 {len(skill_options)} 个选项")
        
        return ResponseUtil.success(
            msg="获取技能选项成功",
            data=skill_options
        )
        
    except ValidationException as e:
        logger.error(f"获取技能选项参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"获取技能选项查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取技能选项业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取技能选项异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
