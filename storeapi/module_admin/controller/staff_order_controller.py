"""
员工端订单管理控制器
"""
import logging
from typing import Optional, List
from fastapi import APIRouter, Depends, Query, Form, File, UploadFile, Body
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from module_admin.service.staff_order_service import StaffOrderService
from module_admin.service.service_staff_login_service import ServiceStaffLoginService
from module_admin.service.file_service import FileService
from module_admin.entity.vo.staff_order_vo import StaffOrderListResponseModel
from config.get_db import get_db
from utils.response_util import ResponseUtil
from utils.log_util import logger
from exceptions.exception import QueryException, BusinessException, ValidationException

logger = logging.getLogger(__name__)


# 请求模型
class StartServiceRequest(BaseModel):
    order_number: str
    files: List[dict] = []


class UploadServiceBeforeImagesRequest(BaseModel):
    order_number: str
    files: List[dict] = []


class EndServiceRequest(BaseModel):
    order_number: str
    service_after_files: List[dict] = []
    signature_files: List[dict] = []


# 使用员工认证的API路由前缀
staff_order_controller = APIRouter(
    prefix='/api/v1/staff-order'
)


@staff_order_controller.post('/getOrderList', summary="获取员工订单列表")
async def get_staff_order_list(
    page: int = Form(1, description="页码"),
    size: int = Form(20, description="每页数量"),
    status: Optional[str] = Form(None, description="订单状态筛选"),
    keyword: Optional[str] = Form(None, description="关键词搜索"),
    store_id: str = Form(..., description="门店ID（必传）"),
    store_uuid: str = Form(..., description="门店UUID（必传）"),
    db: AsyncSession = Depends(get_db),
    current_staff = Depends(ServiceStaffLoginService.get_current_staff)
):
    """
    获取员工订单列表

    员工端专用接口，根据当前登录员工和选中门店获取相关订单列表
    支持按状态筛选和关键词搜索

    Args:
        page: 页码，默认1
        size: 每页数量，默认20
        status: 订单状态筛选，可选
        keyword: 关键词搜索，可选
        store_id: 门店ID，必传
        store_uuid: 门店UUID，必传
        db: 数据库会话
        current_staff: 当前登录员工

    Returns:
        当前门店的订单列表数据，包含订单信息和统计数据

    状态说明：
    - 10: 已接单
    - 20: 派单待确认
    - 30: 拒绝接单
    - 40: 已派单
    - 50: 执行中
    - 60: 开始服务
    - 70: 服务结束
    - 80: 已完成
    - 90: 已评价
    - 99: 已取消
    """
    try:
        logger.info(f"员工订单列表请求，员工ID: {current_staff.id}, 页码: {page}, 状态: {status}, 门店ID: {store_id}")

        # 参数验证
        if page < 1:
            raise ValidationException(message="页码必须大于0")
        if size < 1 or size > 100:
            raise ValidationException(message="每页数量必须在1-100之间")
        if not store_id or not store_uuid:
            raise ValidationException(message="门店信息不能为空")

        # 调用服务层获取订单列表（传入完整的员工对象）
        result = await StaffOrderService.get_staff_order_list_service(
            db, current_staff, page, size, status, keyword, store_id, store_uuid
        )
        
        logger.info(f"员工订单列表获取成功，员工ID: {current_staff.id}")
        return ResponseUtil.success(data=result, msg="获取订单列表成功")
        
    except ValidationException as e:
        logger.error(f"员工订单列表参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code)
    except QueryException as e:
        logger.error(f"员工订单列表查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"员工订单列表业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"员工订单列表异常: {str(e)}")
        return ResponseUtil.error(msg=f"获取订单列表失败: {str(e)}", code=500)


@staff_order_controller.get('/getOrderList', summary="获取员工订单列表(GET)")
async def get_staff_order_list_get(
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    status: Optional[str] = Query(None, description="订单状态筛选"),
    keyword: Optional[str] = Query(None, description="关键词搜索"),
    store_id: str = Query(..., description="门店ID（必传）"),
    store_uuid: str = Query(..., description="门店UUID（必传）"),
    db: AsyncSession = Depends(get_db),
    current_staff = Depends(ServiceStaffLoginService.get_current_staff)
):
    """
    获取员工订单列表(GET方式)
    
    与POST接口功能相同，提供GET方式访问
    """
    try:
        logger.info(f"员工订单列表GET请求，员工ID: {current_staff.id}, 页码: {page}, 状态: {status}, 门店ID: {store_id}")

        # 参数验证
        if page < 1:
            raise ValidationException(message="页码必须大于0")
        if size < 1 or size > 100:
            raise ValidationException(message="每页数量必须在1-100之间")
        if not store_id or not store_uuid:
            raise ValidationException(message="门店信息不能为空")

        # 调用服务层获取订单列表（传入完整的员工对象）
        result = await StaffOrderService.get_staff_order_list_service(
            db, current_staff, page, size, status, keyword, store_id, store_uuid
        )
        
        logger.info(f"员工订单列表GET获取成功，员工ID: {current_staff.id}")
        return ResponseUtil.success(data=result, msg="获取订单列表成功")
        
    except ValidationException as e:
        logger.error(f"员工订单列表GET参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code)
    except QueryException as e:
        logger.error(f"员工订单列表GET查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"员工订单列表GET业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"员工订单列表GET异常: {str(e)}")
        return ResponseUtil.error(msg=f"获取订单列表失败: {str(e)}", code=500)


@staff_order_controller.get('/getOrderStatistics', summary="获取员工订单统计")
async def get_staff_order_statistics(
    db: AsyncSession = Depends(get_db),
    current_staff = Depends(ServiceStaffLoginService.get_current_staff)
):
    """
    获取员工订单统计信息
    
    返回员工的订单统计数据，包括：
    - 待确认订单数
    - 服务中订单数  
    - 已完成订单数
    - 总订单数
    - 总佣金
    """
    try:
        logger.info(f"员工订单统计请求，员工ID: {current_staff.id}")
        
        # 调用服务层获取统计信息
        result = await StaffOrderService.get_staff_order_statistics_service(
            db, current_staff.id
        )
        
        logger.info(f"员工订单统计获取成功，员工ID: {current_staff.id}")
        return ResponseUtil.success(data=result, msg="获取订单统计成功")
        
    except QueryException as e:
        logger.error(f"员工订单统计查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"员工订单统计业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"员工订单统计异常: {str(e)}")
        return ResponseUtil.error(msg=f"获取订单统计失败: {str(e)}", code=500)


@staff_order_controller.get('/getCommissionStatistics', summary="获取员工提成统计")
async def get_commission_statistics(
    store_uuid: Optional[str] = Query(None, description="门店UUID（可选）"),
    db: AsyncSession = Depends(get_db),
    current_staff = Depends(ServiceStaffLoginService.get_current_staff)
):
    """
    获取员工提成统计信息

    返回员工的提成统计数据，包括：
    - 销售提成：本月销售金额、本月销售提成
    - 服务提成：本月服务单数、本月服务提成

    只统计已完成订单（状态为80或90）的提成
    """
    try:
        logger.info(f"员工提成统计请求，员工ID: {current_staff.id}, 门店UUID: {store_uuid}")

        # 调用服务层获取提成统计信息
        result = await StaffOrderService.get_commission_statistics_service(
            db, current_staff.id, store_uuid
        )

        logger.info(f"员工提成统计获取成功，员工ID: {current_staff.id}")
        return ResponseUtil.success(data=result, msg="获取提成统计成功")

    except QueryException as e:
        logger.error(f"员工提成统计查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"员工提成统计业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"员工提成统计异常: {str(e)}")
        return ResponseUtil.error(msg=f"获取提成统计失败: {str(e)}", code=500)


@staff_order_controller.post('/acceptOrder', summary="员工确认接单")
async def accept_order(
    order_number: str = Form(..., description="订单编号"),
    db: AsyncSession = Depends(get_db),
    current_staff = Depends(ServiceStaffLoginService.get_current_staff)
):
    """
    员工确认接单

    将订单状态从20（派单待确认）更新为40（已派单）
    只有状态为20的订单才能被接单
    """
    try:
        # 缓存员工信息，避免后续重复查询
        staff_id = current_staff.id
        logger.info(f"员工接单请求，员工ID: {staff_id}, 订单号: {order_number}")

        # 参数验证
        if not order_number or not order_number.strip():
            raise ValidationException(message="订单编号不能为空")

        # 调用服务层处理接单逻辑
        result = await StaffOrderService.accept_order_service(
            db, staff_id, order_number.strip()
        )

        logger.info(f"员工接单成功，员工ID: {staff_id}, 订单号: {order_number}")
        return ResponseUtil.success(data=result, msg="接单成功")

    except ValidationException as e:
        logger.error(f"员工接单验证异常: {e.message}")
        return ResponseUtil.failure(msg=e.message)
    except QueryException as e:
        logger.error(f"员工接单查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"员工接单业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"员工接单异常: {str(e)}")
        return ResponseUtil.error(msg=f"接单失败: {str(e)}", code=500)


@staff_order_controller.post('/rejectOrder', summary="员工拒绝接单")
async def reject_order(
    order_number: str = Form(..., description="订单编号"),
    reject_reason: str = Form("", description="拒绝原因"),
    db: AsyncSession = Depends(get_db),
    current_staff = Depends(ServiceStaffLoginService.get_current_staff)
):
    """
    员工拒绝接单

    将订单状态从20（派单待确认）更新为30（拒绝接单）
    只有状态为20的订单才能被拒绝
    """
    try:
        # 缓存员工信息，避免后续重复查询
        staff_id = current_staff.id
        logger.info(f"员工拒绝接单请求，员工ID: {staff_id}, 订单号: {order_number}")

        # 参数验证
        if not order_number or not order_number.strip():
            raise ValidationException(message="订单编号不能为空")

        # 调用服务层处理拒绝接单逻辑
        result = await StaffOrderService.reject_order_service(
            db, staff_id, order_number.strip(), reject_reason
        )

        logger.info(f"员工拒绝接单成功，员工ID: {staff_id}, 订单号: {order_number}")
        return ResponseUtil.success(data=result, msg="拒绝接单成功")

    except ValidationException as e:
        logger.error(f"员工拒绝接单验证异常: {e.message}")
        return ResponseUtil.failure(msg=e.message)
    except QueryException as e:
        logger.error(f"员工拒绝接单查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"员工拒绝接单业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"员工拒绝接单异常: {str(e)}")
        return ResponseUtil.error(msg=f"拒绝接单失败: {str(e)}", code=500)


@staff_order_controller.post('/updateOrderStatus', summary="员工更新订单状态")
async def update_order_status(
    order_number: str = Form(..., description="订单编号"),
    new_status: int = Form(..., description="新状态"),
    db: AsyncSession = Depends(get_db),
    current_staff = Depends(ServiceStaffLoginService.get_current_staff)
):
    """
    员工更新订单状态

    用于员工在服务过程中更新订单状态，如：
    - 40（已派单）→ 50（执行中）：前往服务地址
    - 50（执行中）→ 60（开始服务）：开始服务
    - 60（开始服务）→ 70（服务结束）：服务结束
    """
    try:
        # 缓存员工信息，避免后续重复查询
        staff_id = current_staff.id
        logger.info(f"员工更新订单状态请求，员工ID: {staff_id}, 订单号: {order_number}, 新状态: {new_status}")

        # 参数验证
        if not order_number or not order_number.strip():
            raise ValidationException(message="订单编号不能为空")

        if new_status not in [40, 50, 60, 70, 80]:
            raise ValidationException(message="订单状态参数无效")

        # 调用服务层处理状态更新逻辑
        result = await StaffOrderService.update_order_status_service(
            db, staff_id, order_number.strip(), new_status
        )

        logger.info(f"员工更新订单状态成功，员工ID: {staff_id}, 订单号: {order_number}, 新状态: {new_status}")
        return ResponseUtil.success(data=result, msg="状态更新成功")

    except ValidationException as e:
        logger.error(f"员工更新订单状态验证异常: {e.message}")
        return ResponseUtil.failure(msg=e.message)
    except QueryException as e:
        logger.error(f"员工更新订单状态查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"员工更新订单状态业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"员工更新订单状态异常: {str(e)}")
        return ResponseUtil.error(msg=f"状态更新失败: {str(e)}", code=500)


@staff_order_controller.post('/startService', summary="员工开始服务")
async def start_service(
    request_data: StartServiceRequest,
    db: AsyncSession = Depends(get_db),
    current_staff = Depends(ServiceStaffLoginService.get_current_staff)
):
    """
    员工开始服务

    记录服务开始时间并保存开始前环境图文件组ID
    将订单状态从50（执行中）更新为60（开始服务）
    """
    try:
        staff_id = current_staff.id

        # 从请求数据中获取参数
        order_number = request_data.order_number
        files = request_data.files

        logger.info(f"员工开始服务请求，员工ID: {staff_id}, 订单号: {order_number}")

        # 参数验证
        if not order_number or not order_number.strip():
            raise ValidationException(message="订单编号不能为空")

        # 调用服务层处理开始服务逻辑
        result = await StaffOrderService.start_service_service(
            db, staff_id, order_number.strip(), files
        )

        logger.info(f"员工开始服务成功，员工ID: {staff_id}, 订单号: {order_number}")
        return ResponseUtil.success(data=result, msg="开始服务成功")

    except ValidationException as e:
        logger.error(f"员工开始服务验证异常: {e.message}")
        return ResponseUtil.failure(msg=e.message)
    except QueryException as e:
        logger.error(f"员工开始服务查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"员工开始服务业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"员工开始服务异常: {str(e)}")
        return ResponseUtil.error(msg=f"开始服务失败: {str(e)}", code=500)


@staff_order_controller.post('/uploadServiceBeforeImages', summary="上传服务前图片")
async def upload_service_before_images(
    request_data: UploadServiceBeforeImagesRequest,
    db: AsyncSession = Depends(get_db),
    current_staff = Depends(ServiceStaffLoginService.get_current_staff)
):
    """
    上传服务前图片

    在服务过程中上传服务前的图片记录
    """
    try:
        staff_id = current_staff.id

        # 从请求数据中获取参数
        order_number = request_data.order_number
        files = request_data.files

        logger.info(f"员工上传服务前图片请求，员工ID: {staff_id}, 订单号: {order_number}")

        # 参数验证
        if not order_number or not order_number.strip():
            raise ValidationException(message="订单编号不能为空")

        if not files or len(files) == 0:
            raise ValidationException(message="请选择要上传的服务前图片")

        # 创建服务前图片文件组
        service_before_file_id = await FileService.create_file_group_from_upload_results(
            db, files, "服务前图片", str(staff_id)
        )

        if not service_before_file_id:
            raise BusinessException(message="创建服务前图片文件组失败")

        # 调用服务层更新服务前图片
        result = await StaffOrderService.upload_service_before_images_service(
            db, staff_id, order_number.strip(), service_before_file_id
        )

        logger.info(f"员工上传服务前图片成功，员工ID: {staff_id}, 订单号: {order_number}")
        return ResponseUtil.success(data=result, msg="服务前图片上传成功")

    except ValidationException as e:
        logger.error(f"员工上传服务前图片验证异常: {e.message}")
        return ResponseUtil.failure(msg=e.message)
    except QueryException as e:
        logger.error(f"员工上传服务前图片查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"员工上传服务前图片业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"员工上传服务前图片异常: {str(e)}")
        return ResponseUtil.error(msg=f"服务前图片上传失败: {str(e)}", code=500)


@staff_order_controller.post('/endService', summary="员工结束服务")
async def end_service(
    request_data: EndServiceRequest,
    db: AsyncSession = Depends(get_db),
    current_staff = Depends(ServiceStaffLoginService.get_current_staff)
):
    """
    员工结束服务

    记录服务结束时间并上传服务后图片和电子签名
    将订单状态从60（开始服务）更新为80（已完成）
    """
    try:
        staff_id = current_staff.id

        # 从请求数据中获取参数
        order_number = request_data.order_number
        service_after_files = request_data.service_after_files
        signature_files = request_data.signature_files

        logger.info(f"员工结束服务请求，员工ID: {staff_id}, 订单号: {order_number}")

        # 参数验证
        if not order_number or not order_number.strip():
            raise ValidationException(message="订单编号不能为空")

        # 处理服务后图片文件组
        service_after_file_id = None
        if service_after_files and len(service_after_files) > 0:
            service_after_file_id = await FileService.create_file_group_from_upload_results(
                db, service_after_files, "服务后图片", str(staff_id)
            )

            if not service_after_file_id:
                raise BusinessException(message="创建服务后图片文件组失败")

        # 处理电子签名文件组
        signature_file_id = None
        if signature_files and len(signature_files) > 0:
            signature_file_id = await FileService.create_file_group_from_upload_results(
                db, signature_files, "电子签名", str(staff_id)
            )

            if not signature_file_id:
                raise BusinessException(message="创建电子签名文件组失败")

        # 调用服务层处理结束服务逻辑
        result = await StaffOrderService.end_service_service(
            db, staff_id, order_number.strip(), service_after_file_id, signature_file_id
        )

        logger.info(f"员工结束服务成功，员工ID: {staff_id}, 订单号: {order_number}")
        return ResponseUtil.success(data=result, msg="结束服务成功")

    except ValidationException as e:
        logger.error(f"员工结束服务验证异常: {e.message}")
        return ResponseUtil.failure(msg=e.message)
    except QueryException as e:
        logger.error(f"员工结束服务查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"员工结束服务业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"员工结束服务异常: {str(e)}")
        return ResponseUtil.error(msg=f"结束服务失败: {str(e)}", code=500)
