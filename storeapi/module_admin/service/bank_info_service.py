from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text
from typing import List, Dict, Any, Optional
import asyncio

from exceptions.exception import QueryException
from utils.log_util import logger


# 银行信息缓存
_bank_cache = {
    'banks_by_code': {},
    'banks_by_name': {},
    'all_banks': [],
    'last_update': None
}


class BankInfoService:
    """银行信息服务层"""

    @staticmethod
    async def _load_bank_cache(db: AsyncSession) -> None:
        """
        加载银行信息到缓存

        :param db: 数据库会话
        """
        try:
            import time

            # 检查缓存是否需要更新（每小时更新一次）
            current_time = time.time()
            if (_bank_cache['last_update'] and
                current_time - _bank_cache['last_update'] < 3600):
                return

            # 从数据库加载所有银行信息
            query = text("SELECT bank_code, bank_name FROM bank_info WHERE status = 1")
            result = await db.execute(query)
            rows = result.fetchall()

            # 清空缓存
            _bank_cache['banks_by_code'].clear()
            _bank_cache['banks_by_name'].clear()
            _bank_cache['all_banks'].clear()

            # 构建缓存
            for row in rows:
                bank_code, bank_name = row[0], row[1]
                bank_info = {'bank_code': bank_code, 'bank_name': bank_name}

                # 按代码索引
                _bank_cache['banks_by_code'][bank_code] = bank_info

                # 按名称索引（支持多种名称格式）
                _bank_cache['banks_by_name'][bank_name] = bank_info

                # 添加简化名称索引
                if '银行' in bank_name:
                    simple_name = bank_name.replace('银行', '')
                    if simple_name:
                        _bank_cache['banks_by_name'][simple_name] = bank_info

                # 添加到全部银行列表
                _bank_cache['all_banks'].append(bank_info)

            _bank_cache['last_update'] = current_time
            logger.info(f"银行信息缓存已更新，共加载 {len(_bank_cache['all_banks'])} 家银行")

        except Exception as e:
            logger.error(f"加载银行信息缓存失败: {str(e)}")

    @staticmethod
    async def get_bank_code_by_name_cached(db: AsyncSession, bank_name: str) -> str:
        """
        根据银行名称从缓存获取银行代码（优先使用缓存）

        :param db: 数据库会话
        :param bank_name: 银行名称
        :return: 银行代码
        """
        if not bank_name:
            return ''

        try:
            # 确保缓存已加载
            await BankInfoService._load_bank_cache(db)

            # 精确匹配
            if bank_name in _bank_cache['banks_by_name']:
                return _bank_cache['banks_by_name'][bank_name]['bank_code']

            # 模糊匹配
            for cached_name, bank_info in _bank_cache['banks_by_name'].items():
                if bank_name in cached_name or cached_name in bank_name:
                    return bank_info['bank_code']

            # 如果缓存中没有找到，回退到数据库查询
            return await BankInfoService.get_bank_code_by_name(db, bank_name)

        except Exception as e:
            logger.error(f"从缓存获取银行代码失败: {str(e)}")
            # 回退到数据库查询
            return await BankInfoService.get_bank_code_by_name(db, bank_name)

    @staticmethod
    async def get_bank_code_by_name(db: AsyncSession, bank_name: str) -> str:
        """
        根据银行名称从数据库获取银行代码
        
        :param db: 数据库会话
        :param bank_name: 银行名称
        :return: 银行代码
        """
        if not bank_name:
            return ''
        
        try:
            # 先尝试精确匹配
            query = text("SELECT bank_code FROM bank_info WHERE bank_name = :bank_name AND status = 1")
            result = await db.execute(query, {"bank_name": bank_name})
            bank_code = result.scalar_one_or_none()
            
            if bank_code:
                return bank_code
            
            # 如果精确匹配失败，尝试模糊匹配
            query = text("SELECT bank_code FROM bank_info WHERE bank_name LIKE :bank_name AND status = 1 LIMIT 1")
            result = await db.execute(query, {"bank_name": f"%{bank_name}%"})
            bank_code = result.scalar_one_or_none()
            
            if bank_code:
                return bank_code
            
            # 反向模糊匹配
            query = text("SELECT bank_code FROM bank_info WHERE :bank_name LIKE CONCAT('%', bank_name, '%') AND status = 1 LIMIT 1")
            result = await db.execute(query, {"bank_name": bank_name})
            bank_code = result.scalar_one_or_none()
            
            return bank_code or ''
            
        except Exception as e:
            logger.error(f"从数据库获取银行代码失败: {str(e)}")
            return ''

    @staticmethod
    async def get_bank_info_by_code(db: AsyncSession, bank_code: str) -> Optional[Dict[str, Any]]:
        """
        根据银行代码获取银行信息
        
        :param db: 数据库会话
        :param bank_code: 银行代码
        :return: 银行信息
        """
        if not bank_code:
            return None
        
        try:
            query = text("SELECT bank_code, bank_name FROM bank_info WHERE bank_code = :bank_code AND status = 1")
            result = await db.execute(query, {"bank_code": bank_code})
            row = result.fetchone()
            
            if row:
                return {
                    "bank_code": row[0],
                    "bank_name": row[1]
                }
            
            return None
            
        except Exception as e:
            logger.error(f"根据银行代码获取银行信息失败: {str(e)}")
            return None

    @staticmethod
    async def get_all_banks(db: AsyncSession) -> List[Dict[str, Any]]:
        """
        获取所有启用的银行信息
        
        :param db: 数据库会话
        :return: 银行信息列表
        """
        try:
            query = text("SELECT bank_code, bank_name FROM bank_info WHERE status = 1 ORDER BY bank_name")
            result = await db.execute(query)
            rows = result.fetchall()
            
            banks = []
            for row in rows:
                banks.append({
                    "bank_code": row[0],
                    "bank_name": row[1]
                })
            
            return banks
            
        except Exception as e:
            logger.error(f"获取银行信息列表失败: {str(e)}")
            return []

    @staticmethod
    async def get_bank_info_by_card_number(db: AsyncSession, card_number: str) -> Dict[str, str]:
        """
        根据银行卡号前缀判断银行信息（结合数据库查询）
        
        :param db: 数据库会话
        :param card_number: 银行卡号
        :return: 银行信息
        """
        if not card_number:
            return {}
        
        # 银行卡号前缀对应的银行代码
        bank_prefixes = {
            # 工商银行
            '622202': '************',
            '622208': '************',
            '955880': '************',
            
            # 农业银行
            '622848': '************',
            '622845': '************',
            '95599': '************',
            
            # 中国银行
            '621661': '************',
            '621662': '************',
            '456351': '************',
            
            # 建设银行
            '622700': '************',
            '436742': '************',
            '622280': '************',
            
            # 交通银行
            '622260': '************',
            '622261': '************',
            
            # 招商银行
            '622575': '************',
            '622577': '************',
            '621483': '************',
            
            # 民生银行
            '622622': '*********013',
            '415599': '*********013',
            
            # 中信银行
            '622690': '310290000013',
            '622691': '310290000013',
            
            # 光大银行
            '622655': '************',
            '622650': '************',
            
            # 平安银行
            '622155': '************',
            '622156': '************',
            
            # 兴业银行
            '622902': '************',
            '622901': '************',
            
            # 浦发银行
            '622521': '************',
            '622522': '************',
        }
        
        # 检查卡号前缀
        for prefix, bank_code in bank_prefixes.items():
            if card_number.startswith(prefix):
                # 从数据库获取银行名称
                bank_info = await BankInfoService.get_bank_info_by_code(db, bank_code)
                if bank_info:
                    return {
                        'name': bank_info['bank_name'],
                        'code': bank_info['bank_code']
                    }
                else:
                    return {'name': '', 'code': bank_code}
        
        # 如果没有匹配的前缀，返回空信息
        return {'name': '', 'code': ''}
