"""
门店详细信息数据模型
用于门店详细信息的数据库映射
"""
from datetime import datetime
from sqlalchemy import Column, String, Integer, DECIMAL, DateTime, BigInteger, Text
from config.database import Base


class StoreInfo(Base):
    """门店详细信息表"""

    __tablename__ = 'store_info'

    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='ID')
    
    # 关联信息
    store_id = Column(BigInteger, nullable=False, comment='门店ID')
    
    # 基本信息
    type = Column(String(255), nullable=True, comment='类型')
    nature = Column(String(255), nullable=True, comment='性质')
    unit_nature = Column(String(50), nullable=True, comment='单位性质')
    business_license = Column(String(255), nullable=True, comment='营业执照')
    store_specifications = Column(String(255), nullable=True, comment='门店规格')
    desc_phone_type = Column(String(255), nullable=True, comment='描述电话类型')
    website_phone_type = Column(String(255), nullable=True, comment='网站电话类型')
    
    # 发票信息
    is_invoice = Column(Integer, nullable=True, comment='是否开票')
    invoice_title = Column(String(155), nullable=True, comment='发票抬头')
    invoice_code = Column(String(255), nullable=True, comment='发票代码')
    invoice_phone = Column(String(20), nullable=True, comment='发票电话')
    invoice_address = Column(String(255), nullable=True, comment='发票地址')
    invoice_bank = Column(String(100), nullable=True, comment='发票银行')
    invoice_account = Column(String(100), nullable=True, comment='发票账户')
    
    # 业务配置
    is_bd_pos = Column(Integer, nullable=True, comment='是否BD POS')
    location_synchronize_status = Column(Integer, nullable=True, comment='位置同步状态')
    health_degree = Column(DECIMAL(5, 2), nullable=True, comment='健康度')
    is_clean_keeping = Column(Integer, nullable=True, default=0, comment='是否保洁')
    is_housekeeping_staff = Column(Integer, nullable=True, default=0, comment='是否家政人员')
    is_part_time_job = Column(Integer, nullable=True, default=0, comment='是否兼职')
    is_free = Column(Integer, nullable=True, default=0, comment='是否免费')
    is_discount = Column(Integer, nullable=True, default=0, comment='是否折扣')
    user_num_limit = Column(Integer, nullable=True, comment='用户数量限制')
    pay_num_limit = Column(Integer, nullable=True, comment='支付数量限制')
    
    def __repr__(self):
        return f"<StoreInfo(id={self.id}, store_id={self.store_id})>"
