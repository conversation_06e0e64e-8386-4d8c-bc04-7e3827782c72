from fastapi import APIRouter, Depends, Request, Query, Form, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any
from config.get_db import get_db
from module_admin.service.training_service import TrainingService
from module_admin.service.auth_adapter import AuthAdapter
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, ResourceNotFoundException, BusinessException, ValidationException

# 使用统一的API路由前缀格式
training_controller = APIRouter(prefix='/api/v1/training', dependencies=[Depends(AuthAdapter.get_current_user)])

@training_controller.get('/course/getSkillCourseList', summary="获取技能课程列表")
async def get_skill_course_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    type: Optional[str] = Query(None, description="课程类型"),
    keyword: Optional[str] = Query(None, description="关键词"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取技能课程列表接口
    
    分页获取技能课程列表，支持按类型和关键词筛选
    
    原始API路径: /trainingv3/course/getSkillCourseList
    """
    try:
        # 调用服务层获取技能课程列表
        result = await TrainingService.get_skill_course_list_service(query_db, page, size, type, keyword)
        
        return ResponseUtil.success(
            msg="获取技能课程列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询技能课程列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取技能课程列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取技能课程列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@training_controller.get('/watch/getList', summary="获取观看列表")
async def get_watch_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    user_uuid: Optional[str] = Query(None, description="用户UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取观看列表接口
    
    分页获取视频观看记录列表，支持按用户UUID筛选
    
    原始API路径: /collegev3/watch/getList
    """
    try:
        # 获取当前用户信息
        current_user = await AuthAdapter.get_current_user(request)
        
        # 如果未指定用户UUID，则使用当前用户的UUID
        if not user_uuid:
            user_uuid = current_user.get("user_uuid")
        
        # 调用服务层获取观看列表
        result = await TrainingService.get_watch_list_service(query_db, page, size, user_uuid)
        
        return ResponseUtil.success(
            msg="获取观看列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询观看列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取观看列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取观看列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@training_controller.get('/course/getOne', summary="获取单个课程信息")
async def get_course_one(
    request: Request,
    uuid: str = Query(..., description="课程UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取单个课程信息接口
    
    根据UUID获取课程的详细信息，包括章节、视频和评论等
    
    原始API路径: /collegev3/course/getOne
    """
    try:
        # 调用服务层获取单个课程信息
        result = await TrainingService.get_course_one_service(query_db, uuid)
        
        return ResponseUtil.success(
            msg="获取单个课程信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取单个课程信息数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"课程资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询单个课程信息失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取单个课程信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取单个课程信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@training_controller.get('/banner/getAll', summary="获取所有横幅信息")
async def get_banner_all(
    request: Request,
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取所有横幅信息接口
    
    获取所有有效的横幅信息列表
    
    原始API路径: /collegev3/banner/getAll
    """
    try:
        # 调用服务层获取所有横幅信息
        result = await TrainingService.get_banner_all_service(query_db)
        
        return ResponseUtil.success(
            msg="获取所有横幅信息成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询所有横幅信息失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取所有横幅信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取所有横幅信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@training_controller.get('/video/saveLog', summary="保存视频观看日志")
async def save_video_log(
    request: Request,
    course_uuid: str = Query(..., description="课程UUID"),
    video_uuid: str = Query(..., description="视频UUID"),
    watch_duration: int = Query(0, description="观看时长（秒）"),
    progress: int = Query(0, description="观看进度（百分比）"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """保存视频观看日志接口
    
    记录用户观看视频的日志信息，包括观看时长和进度
    
    原始API路径: /collegeV3/video/saveLog
    """
    try:
        # 获取当前用户信息
        current_user = await AuthAdapter.get_current_user(request)
        
        # 构建日志数据
        log_data = {
            "user_uuid": current_user.get("user_uuid"),
            "course_uuid": course_uuid,
            "video_uuid": video_uuid,
            "watch_duration": watch_duration,
            "progress": progress
        }
        
        # 调用服务层保存视频观看日志
        result = await TrainingService.save_video_log_service(query_db, log_data)
        
        return ResponseUtil.success(
            msg="保存视频观看日志成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"保存视频观看日志数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"保存视频观看日志业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"保存视频观看日志异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@training_controller.get('/course/getList', summary="获取课程列表")
async def get_course_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    type: Optional[str] = Query(None, description="课程类型"),
    keyword: Optional[str] = Query(None, description="关键词"),
    category_id: Optional[str] = Query(None, description="分类ID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取课程列表接口
    
    分页获取课程列表，支持按类型、关键词和分类ID筛选
    
    原始API路径: /collegev3/course/getList
    """
    try:
        # 调用服务层获取课程列表
        result = await TrainingService.get_course_list_service(query_db, page, size, type, keyword, category_id)
        
        return ResponseUtil.success(
            msg="获取课程列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询课程列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取课程列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取课程列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@training_controller.get('/recordCourse/getList', summary="获取录播课程列表")
async def get_record_course_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    type: Optional[str] = Query(None, description="课程类型"),
    keyword: Optional[str] = Query(None, description="关键词"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取录播课程列表接口
    
    分页获取录播课程列表，支持按类型和关键词筛选
    
    原始API路径: /trainingV3/recordCourse/getList
    """
    try:
        # 调用服务层获取录播课程列表
        result = await TrainingService.get_record_course_list_service(query_db, page, size, type, keyword)
        
        return ResponseUtil.success(
            msg="获取录播课程列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询录播课程列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取录播课程列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取录播课程列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@training_controller.get('/recruitmentPoster/getShareNum', summary="获取招聘海报分享次数")
async def get_recruitment_poster_share_num(
    request: Request,
    poster_uuid: str = Query(..., description="海报UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取招聘海报分享次数接口
    
    根据海报UUID获取招聘海报的分享次数
    
    原始API路径: /trainingV3/recruitmentPoster/getShareNum
    """
    try:
        # 调用服务层获取招聘海报分享次数
        result = await TrainingService.get_recruitment_poster_share_num_service(query_db, poster_uuid)
        
        return ResponseUtil.success(
            msg="获取招聘海报分享次数成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取招聘海报分享次数数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询招聘海报分享次数失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取招聘海报分享次数业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取招聘海报分享次数异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@training_controller.post('/findSignInList', summary="获取签到列表")
async def find_sign_in_list(
    request: Request,
    page: int = Form(1, description="页码"),
    size: int = Form(20, description="每页数量"),
    course_uuid: Optional[str] = Form(None, description="课程UUID"),
    date: Optional[str] = Form(None, description="日期"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取签到列表接口
    
    分页获取签到列表，支持按课程UUID和日期筛选
    
    原始API路径: /trainingV3/findSignInList
    """
    try:
        # 调用服务层获取签到列表
        result = await TrainingService.find_sign_in_list_service(query_db, page, size, course_uuid, date)
        
        return ResponseUtil.success(
            msg="获取签到列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询签到列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取签到列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取签到列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@training_controller.post('/studentClue/createFollow', summary="创建学生线索跟进记录")
async def create_student_clue_follow(
    request: Request,
    clue_uuid: str = Form(..., description="线索UUID"),
    content: str = Form(..., description="跟进内容"),
    follow_type: str = Form("1", description="跟进类型"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """创建学生线索跟进记录接口
    
    为学生线索添加跟进记录
    
    原始API路径: /trainingV3/studentClue/createFollow
    """
    try:
        # 获取当前用户信息
        current_user = await AuthAdapter.get_current_user(request)
        
        # 构建跟进数据
        follow_data = {
            "clue_uuid": clue_uuid,
            "user_uuid": current_user.get("user_uuid"),
            "content": content,
            "follow_type": follow_type
        }
        
        # 调用服务层创建学生线索跟进记录
        result = await TrainingService.create_student_clue_follow_service(query_db, follow_data)
        
        return ResponseUtil.success(
            msg="创建学生线索跟进记录成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"创建学生线索跟进记录数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"创建学生线索跟进记录业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"创建学生线索跟进记录异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@training_controller.post('/studentClue/updateOwner', summary="更新学生线索负责人")
async def update_student_clue_owner(
    request: Request,
    clue_uuid: str = Form(..., description="线索UUID"),
    user_uuid: str = Form(..., description="用户UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """更新学生线索负责人接口
    
    更新学生线索的负责人
    
    原始API路径: /trainingV3/studentClue/updateOwner
    """
    try:
        # 调用服务层更新学生线索负责人
        result = await TrainingService.update_student_clue_owner_service(query_db, clue_uuid, user_uuid)
        
        return ResponseUtil.success(
            msg="更新学生线索负责人成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"更新学生线索负责人数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"更新学生线索负责人业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"更新学生线索负责人异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@training_controller.get('/merchant/courses', summary="获取商家培训课程列表")
async def get_merchant_training_courses(
    request: Request,
    limit: int = Query(10, description="限制返回数量"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取商家培训课程列表接口（用于商家端首页展示）

    获取training_courses表中的培训课程数据，用于商家端首页培训功能展示
    """
    try:
        # 调用服务层获取商家培训课程列表
        result = await TrainingService.get_merchant_training_courses_service(query_db, limit)

        return ResponseUtil.success(
            msg="获取商家培训课程列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询商家培训课程列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取商家培训课程列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取商家培训课程列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@training_controller.get('/getTrainingCategory', summary="获取培训分类列表")
async def get_training_category(
    request: Request,
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取培训分类列表接口

    获取所有培训分类，支持多级分类结构

    原始API路径: /api/v1/training/getTrainingCategory
    """
    try:
        # 调用服务层获取培训分类列表
        result = await TrainingService.get_training_category_service(query_db)

        return ResponseUtil.success(
            msg="获取培训分类列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询培训分类列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取培训分类列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取培训分类列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@training_controller.get('/getTrainingList', summary="获取培训课程列表")
async def get_training_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    keyword: Optional[str] = Query(None, description="关键词"),
    category_id: Optional[str] = Query(None, description="分类ID"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取培训课程列表接口

    分页获取培训课程列表，支持按关键词、分类、时间范围筛选

    原始API路径: /api/v1/training/getTrainingList
    """
    try:
        # 调用服务层获取培训课程列表
        result = await TrainingService.get_training_list_service(
            query_db, page, size, keyword, category_id, start_date, end_date
        )

        return ResponseUtil.success(
            msg="获取培训课程列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询培训课程列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取培训课程列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取培训课程列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
