from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, Optional, List
from datetime import datetime
import re
import uuid
import json
import httpx

from module_admin.dao.account_application_dao import AccountApplicationDao
from module_admin.service.bank_info_service import BankInfoService
from module_admin.service.area_code_service import AreaCodeService
from module_admin.service.payment_service import PaymentService
from module_admin.entity.vo.account_application_vo import (
    AccountApplicationSubmitVO,
    AccountApplicationDetailVO,
    AccountApplicationListVO,
    AccountApplicationReviewVO,
    AccountApplicationStatusVO
)
from exceptions.exception import ValidationException, QueryException, BusinessException
from utils.log_util import logger
from utils.baidu_ocr_util import baidu_ocr_util
from config.env import YeepaySettings


class AccountApplicationService:
    """开户申请服务层"""

    @staticmethod
    def _extract_detailed_address(full_address: str, province: str = "", city: str = "", district: str = "") -> str:
        """
        从完整地址中提取详细地址，去掉省市区信息

        :param full_address: 完整地址
        :param province: 省份名称
        :param city: 城市名称
        :param district: 区县名称
        :return: 详细地址
        """
        if not full_address:
            return ""

        detailed_address = full_address

        # 去掉省份信息
        if province and province in detailed_address:
            detailed_address = detailed_address.replace(province, "")

        # 去掉城市信息
        if city and city in detailed_address:
            detailed_address = detailed_address.replace(city, "")

        # 去掉区县信息
        if district and district in detailed_address:
            detailed_address = detailed_address.replace(district, "")

        # 使用正则表达式去掉常见的省市区关键词
        patterns = [
            r'.*?省',  # 去掉省
            r'.*?市',  # 去掉市（但要小心不要去掉街道中的"市"）
            r'.*?区',  # 去掉区
            r'.*?县',  # 去掉县
            r'.*?自治区',  # 去掉自治区
            r'.*?特别行政区',  # 去掉特别行政区
        ]

        for pattern in patterns:
            # 只在地址开头匹配，避免误删
            if re.match(pattern, detailed_address):
                detailed_address = re.sub(pattern, '', detailed_address, count=1)

        # 清理多余的空格和标点
        detailed_address = detailed_address.strip().lstrip('，,。.')

        logger.info(f"地址处理: {full_address} -> {detailed_address}")
        return detailed_address

    @staticmethod
    async def submit_application(
        db: AsyncSession,
        application_data: AccountApplicationSubmitVO,
        mobile: str
    ) -> Dict[str, Any]:
        """
        提交开户申请

        :param db: 数据库会话
        :param application_data: 申请数据
        :param mobile: 手机号
        :return: 申请结果
        """
        try:
            # 使用传入的手机号，而不是application_data中的手机号
            actual_mobile = mobile

            # 解析前端传递的OCR识别结果
            id_card_info, bank_card_info = AccountApplicationService._parse_ocr_data(application_data)

            # 检查是否有重复的待审核申请（使用识别出的身份证号）
            if id_card_info.get('id_number'):
                has_duplicate = await AccountApplicationDao.check_duplicate_application(
                    db, actual_mobile, id_card_info['id_number']
                )

                if has_duplicate:
                    raise ValidationException(message="您已有待审核的开户申请，请勿重复提交")

            # 根据身份证号解析省市区信息
            area_info = {}
            if id_card_info.get('id_number'):
                area_info = await AreaCodeService.get_area_info_by_id_card(
                    db, id_card_info['id_number']
                )

            # 处理银行代码查询
            bank_name = bank_card_info.get('bank_name', '')
            bank_code = bank_card_info.get('bank_code', '')

            # 如果OCR没有获取到银行代码，根据银行名称查询
            if bank_name and not bank_code:
                bank_code = await AccountApplicationService._get_bank_code_by_name_async(db, bank_name)

            # 获取省市区信息和编码
            province_name = id_card_info.get('province') or area_info.get('province_name', '')
            city_name = id_card_info.get('city') or area_info.get('city_name', '')
            district_name = id_card_info.get('district') or area_info.get('district_name', '')

            # 获取省市区编码
            province_code = area_info.get('province_code', '')
            city_code = area_info.get('city_code', '')
            district_code = area_info.get('district_code', '')

            # 处理地址信息，从完整地址中去掉省市区，只保留详细地址
            full_address = id_card_info.get('address', '')
            detailed_address = AccountApplicationService._extract_detailed_address(
                full_address, province_name, city_name, district_name
            )

            # 构建完整的申请数据，全部从OCR识别结果中获取
            application_dict = {
                # 基本信息（全部从OCR识别结果获取）
                'name': id_card_info.get('name', ''),
                'id_number': id_card_info.get('id_number', ''),
                'gender': id_card_info.get('gender', ''),
                'birthday': id_card_info.get('birthday', ''),
                'address': detailed_address,  # 使用处理后的详细地址

                # 省市区信息和编码
                'province': province_name,
                'city': city_name,
                'district': district_name,
                'province_code': province_code,
                'city_code': city_code,
                'district_code': district_code,

                # 身份证背面信息
                'issuing_authority': id_card_info.get('issuing_authority', ''),
                'validity_period': id_card_info.get('validity_period', ''),

                # 银行信息（从银行卡OCR获取和数据库查询）
                'bank_name': bank_name,
                'bank_code': bank_code,
                'account_no': (bank_card_info.get('account_no', '') or '').replace(' ', ''),  # 去除所有空格
                'account_name': id_card_info.get('name', ''),  # 使用姓名作为账户名称
                'account_type': 'pCard',  # 默认为个人借记卡

                # 照片URL（从前端提交的数据中获取）
                'id_card_front_url': application_data.id_card_front_url or '',
                'id_card_back_url': application_data.id_card_back_url or '',
                'bank_card_url': application_data.bank_card_url or '',

                # 其他字段暂时为空，后续可以通过管理后台补充
                'occupation': '',
                'company_name': '',
                'annual_income': None
            }

            # 调用易宝开户接口
            logger.info("开始调用易宝开户接口")
            yeepay_result = await AccountApplicationService._call_yeepay_innet_api(application_dict, actual_mobile)

            # 检查易宝开户结果
            if yeepay_result.get("rspCode") != "0000":
                error_msg = yeepay_result.get("rspMsg", "易宝开户失败")
                logger.error(f"易宝开户失败: {error_msg}")
                raise BusinessException(message=f"开户失败: {error_msg}")

            logger.info(f"易宝开户成功，入网客户代码: {yeepay_result.get('subCusCode', 'N/A')}")

            # 解析易宝返回的数据结构
            logger.info(f"解析易宝返回数据结构: {yeepay_result}")

            # 易宝返回的数据可能有两种结构：
            # 1. 直接返回字段（旧格式）
            # 2. 包含msgPublic和msgPrivate的结构（新格式）

            msg_public = yeepay_result.get("msgPublic", {})
            msg_private = yeepay_result.get("msgPrivate", {})

            # 优先从msgPrivate获取客户代码
            sub_cus_code = msg_private.get("subCusCode") or yeepay_result.get("subCusCode")
            if sub_cus_code:
                application_dict["yeepay_customer_code"] = sub_cus_code
                logger.info(f"获取到客户代码: {sub_cus_code}")

            # 从msgPublic获取关键信息
            cus_trace_no = msg_public.get("cusTraceNo") or yeepay_result.get("cusTraceNo", "")
            sys_trace_no = msg_public.get("sysTraceNo") or yeepay_result.get("sysTraceNo", "")
            txn_date = msg_public.get("txnDate") or yeepay_result.get("txnDate", "")
            sys_rsp_time = msg_public.get("sysRspTime") or yeepay_result.get("sysRspTime", "")

            # 存储易宝接口返回的关键信息，用于后续查询
            application_dict["yeepay_cus_trace_no"] = cus_trace_no
            application_dict["yeepay_sys_trace_no"] = sys_trace_no
            application_dict["yeepay_txn_date"] = txn_date
            application_dict["yeepay_rsp_time"] = sys_rsp_time

            logger.info(f"存储易宝关键信息:")
            logger.info(f"  - cusTraceNo: {cus_trace_no}")
            logger.info(f"  - sysTraceNo: {sys_trace_no}")
            logger.info(f"  - txnDate: {txn_date}")
            logger.info(f"  - sysRspTime: {sys_rsp_time}")

            # 创建申请
            application_uuid = await AccountApplicationDao.create_application(
                db, application_dict, actual_mobile
            )

            logger.info(f"用户 {actual_mobile} 提交开户申请成功，UUID: {application_uuid}")

            return {
                "uuid": application_uuid,
                "yeepay_customer_code": yeepay_result.get("subCusCode"),
                "message": "开户申请提交成功，易宝开户已完成"
            }

        except ValidationException:
            raise
        except Exception as e:
            logger.error(f"提交开户申请失败: {str(e)}")
            raise QueryException(message=f"提交开户申请失败: {str(e)}")

    @staticmethod
    async def _recognize_id_card(front_url: str, back_url: str) -> Dict[str, Any]:
        """
        识别身份证信息（OCR）

        :param front_url: 身份证正面照片URL
        :param back_url: 身份证背面照片URL
        :return: 识别结果
        """
        try:
            import aiohttp
            import base64
            import os
            from urllib.parse import urlparse

            logger.info(f"识别身份证信息，正面: {front_url}, 背面: {back_url}")

            # 下载图片并转换为base64
            front_base64 = await AccountApplicationService._url_to_base64(front_url)
            back_base64 = await AccountApplicationService._url_to_base64(back_url)

            if not front_base64:
                logger.error("身份证正面图片转换base64失败")
                return {}

            if not back_base64:
                logger.error("身份证背面图片转换base64失败")
                return {}

            # 调用OCR API识别正面
            front_result = await AccountApplicationService._call_ocr_api(front_base64, 0)  # 0表示正面

            # 调用OCR API识别背面
            back_result = await AccountApplicationService._call_ocr_api(back_base64, 1)  # 1表示背面

            # 解析识别结果
            result = {}

            # 处理正面识别结果
            if front_result:
                front_words = front_result.get('words_result', {})

                if front_words.get('姓名'):
                    result['name'] = front_words['姓名'].get('words', '')

                if front_words.get('公民身份号码'):
                    result['id_number'] = front_words['公民身份号码'].get('words', '')

                if front_words.get('性别'):
                    gender_text = front_words['性别'].get('words', '')
                    result['gender'] = '1' if gender_text == '男' else '2' if gender_text == '女' else ''

                if front_words.get('出生'):
                    birthday_text = front_words['出生'].get('words', '')
                    # 格式化生日，从 "1990年01月01日" 转换为 "1990-01-01"
                    if birthday_text:
                        import re
                        match = re.match(r'(\d{4})年(\d{1,2})月(\d{1,2})日', birthday_text)
                        if match:
                            year, month, day = match.groups()
                            result['birthday'] = f"{year}-{month.zfill(2)}-{day.zfill(2)}"

                if front_words.get('住址'):
                    result['address'] = front_words['住址'].get('words', '')

            # 处理背面识别结果
            if back_result:
                back_words = back_result.get('words_result', {})

                # 背面通常包含签发机关、有效期限等信息
                if back_words.get('签发机关'):
                    result['issuing_authority'] = back_words['签发机关'].get('words', '')

                if back_words.get('有效期限'):
                    result['validity_period'] = back_words['有效期限'].get('words', '')

            logger.info(f"身份证识别成功: {result}")
            return result

        except Exception as e:
            logger.error(f"识别身份证信息失败: {str(e)}")
            return {}

    @staticmethod
    async def _url_to_base64(image_url: str) -> str:
        """
        将图片URL转换为base64

        :param image_url: 图片URL
        :return: base64字符串
        """
        try:
            import aiohttp
            import base64

            async with aiohttp.ClientSession() as session:
                async with session.get(image_url) as response:
                    if response.status == 200:
                        image_data = await response.read()
                        base64_data = base64.b64encode(image_data).decode('utf-8')
                        return base64_data
                    else:
                        logger.error(f"下载图片失败，状态码: {response.status}")
                        return ""

        except Exception as e:
            logger.error(f"图片转base64失败: {str(e)}")
            return ""

    @staticmethod
    async def _call_ocr_api(base64_data: str, card_type: int) -> Dict[str, Any]:
        """
        调用OCR API识别身份证

        :param base64_data: 图片base64数据
        :param card_type: 卡片类型，0表示正面，1表示背面
        :return: 识别结果
        """
        try:
            import aiohttp
            import json

            request_data = {
                "file": base64_data,
                "cardTpe": card_type
            }

            headers = {
                'content-type': 'application/json;charset=UTF-8'
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    'https://agentapi.xiaoyujia.com/files/imgsToIdInfo',
                    data=json.dumps(request_data),
                    headers=headers
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get('image_status') == 'normal':
                            return result
                        else:
                            logger.error(f"OCR识别失败，状态: {result.get('image_status')}")
                            return {}
                    else:
                        logger.error(f"OCR API调用失败，状态码: {response.status}")
                        return {}

        except Exception as e:
            logger.error(f"调用OCR API失败: {str(e)}")
            return {}

    @staticmethod
    async def _recognize_bank_card(card_url: str) -> Dict[str, Any]:
        """
        识别银行卡信息（OCR）

        :param card_url: 银行卡照片URL
        :return: 识别结果
        """
        try:
            logger.info(f"识别银行卡信息: {card_url}")

            # 下载图片并转换为base64
            card_base64 = await AccountApplicationService._url_to_base64(card_url)
            if not card_base64:
                logger.error("银行卡图片转换base64失败")
                return {}

            # 调用银行卡OCR API
            ocr_result = await AccountApplicationService._call_bank_card_ocr_api(card_base64)

            if not ocr_result:
                logger.error("银行卡OCR识别失败")
                return {}

            # 解析识别结果
            result = {}

            # 从识别结果中提取银行卡信息
            if ocr_result.get('bank_card_number'):
                result['account_no'] = ocr_result['bank_card_number'].replace(' ', '')  # 移除空格

                # 优先使用OCR识别的银行名称
                if ocr_result.get('bank_name'):
                    result['bank_name'] = ocr_result['bank_name']
                    # 注意：这里需要数据库会话，暂时使用静态映射
                    result['bank_code'] = AccountApplicationService._get_static_bank_code_by_name(ocr_result['bank_name'])
                else:
                    # 如果OCR没有识别出银行名称，则根据卡号前缀判断
                    # 注意：这里需要数据库会话，暂时使用静态映射
                    bank_info = AccountApplicationService._get_bank_info_by_card_number(result['account_no'])
                    result['bank_name'] = bank_info.get('name', '')
                    result['bank_code'] = bank_info.get('code', '')

            # 持卡人姓名
            if ocr_result.get('holder_name'):
                result['account_name'] = ocr_result['holder_name']
            else:
                result['account_name'] = ''

            logger.info(f"银行卡识别成功: {result}")
            return result

        except Exception as e:
            logger.error(f"识别银行卡信息失败: {str(e)}")
            return {}

    @staticmethod
    async def _call_bank_card_ocr_api(base64_data: str) -> Dict[str, Any]:
        """
        调用百度OCR银行卡识别API

        :param base64_data: 图片base64数据
        :return: 识别结果
        """
        try:
            import aiohttp
            import urllib.parse

            # 百度OCR API配置 - 需要在百度智能云控制台获取
            # 注意：这里需要配置实际的API Key和Secret Key
            API_KEY = "your_api_key"  # 替换为实际的API Key
            SECRET_KEY = "your_secret_key"  # 替换为实际的Secret Key

            # 获取access_token
            access_token = await AccountApplicationService._get_baidu_access_token(API_KEY, SECRET_KEY)

            if not access_token:
                logger.error("获取百度OCR access_token失败")
                return {}

            # 银行卡识别API请求数据
            request_data = {
                "image": base64_data,
                "detect_direction": "true"
            }

            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }

            # 百度OCR银行卡识别API地址
            url = f"https://aip.baidubce.com/rest/2.0/ocr/v1/bankcard?access_token={access_token}"

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url,
                    data=urllib.parse.urlencode(request_data),
                    headers=headers
                ) as response:
                    if response.status == 200:
                        result = await response.json()

                        # 检查是否有错误
                        if 'error_code' in result:
                            logger.error(f"百度OCR API返回错误: {result.get('error_msg', '未知错误')}")
                            return {}

                        # 解析识别结果
                        if 'result' in result:
                            ocr_result = result['result']
                            return {
                                'bank_card_number': ocr_result.get('bank_card_number', ''),
                                'bank_name': ocr_result.get('bank_name', ''),
                                'holder_name': ocr_result.get('holder_name', ''),
                                'valid_date': ocr_result.get('valid_date', ''),
                                'bank_card_type': ocr_result.get('bank_card_type', 0)
                            }
                        else:
                            logger.error("百度OCR API返回结果格式异常")
                            return {}
                    else:
                        logger.error(f"百度OCR API调用失败，状态码: {response.status}")
                        return {}

        except Exception as e:
            logger.error(f"调用百度OCR银行卡识别API失败: {str(e)}")
            return {}

    @staticmethod
    async def _get_baidu_access_token(api_key: str, secret_key: str) -> str:
        """
        获取百度OCR API的access_token

        :param api_key: API Key
        :param secret_key: Secret Key
        :return: access_token
        """
        try:
            import aiohttp
            import urllib.parse

            url = "https://aip.baidubce.com/oauth/2.0/token"

            params = {
                'grant_type': 'client_credentials',
                'client_id': api_key,
                'client_secret': secret_key
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(url, data=params) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get('access_token', '')
                    else:
                        logger.error(f"获取百度access_token失败，状态码: {response.status}")
                        return ""

        except Exception as e:
            logger.error(f"获取百度access_token异常: {str(e)}")
            return ""

    @staticmethod
    def _get_bank_info_by_card_number(card_number: str) -> Dict[str, str]:
        """
        根据银行卡号前缀判断银行信息

        :param card_number: 银行卡号
        :return: 银行信息
        """
        if not card_number:
            return {}

        # 银行卡号前缀对应的银行信息
        bank_prefixes = {
            # 工商银行
            '622202': {'name': '工商银行', 'code': '************'},
            '622208': {'name': '工商银行', 'code': '************'},
            '955880': {'name': '工商银行', 'code': '************'},

            # 农业银行
            '622848': {'name': '农业银行', 'code': '************'},
            '622845': {'name': '农业银行', 'code': '************'},
            '95599': {'name': '农业银行', 'code': '************'},

            # 中国银行
            '621661': {'name': '中国银行', 'code': '************'},
            '621662': {'name': '中国银行', 'code': '************'},
            '456351': {'name': '中国银行', 'code': '************'},

            # 建设银行
            '622700': {'name': '建设银行', 'code': '************'},
            '436742': {'name': '建设银行', 'code': '************'},
            '622280': {'name': '建设银行', 'code': '************'},

            # 交通银行
            '622260': {'name': '交通银行', 'code': '************'},
            '622261': {'name': '交通银行', 'code': '************'},

            # 招商银行
            '622575': {'name': '招商银行', 'code': '************'},
            '622577': {'name': '招商银行', 'code': '************'},
            '621483': {'name': '招商银行', 'code': '************'},

            # 民生银行
            '622622': {'name': '民生银行', 'code': '************'},
            '415599': {'name': '民生银行', 'code': '************'},

            # 中信银行
            '622690': {'name': '中信银行', 'code': '************'},
            '622691': {'name': '中信银行', 'code': '************'},

            # 光大银行
            '622655': {'name': '光大银行', 'code': '************'},
            '622650': {'name': '光大银行', 'code': '************'},

            # 平安银行
            '622155': {'name': '平安银行', 'code': '************'},
            '622156': {'name': '平安银行', 'code': '************'},

            # 兴业银行
            '622902': {'name': '兴业银行', 'code': '************'},
            '622901': {'name': '兴业银行', 'code': '************'},

            # 浦发银行
            '622521': {'name': '浦发银行', 'code': '************'},
            '622522': {'name': '浦发银行', 'code': '************'},
        }

        # 检查卡号前缀
        for prefix, bank_info in bank_prefixes.items():
            if card_number.startswith(prefix):
                return bank_info

        # 如果没有匹配的前缀，返回空信息
        return {'name': '', 'code': ''}

    @staticmethod
    async def _get_bank_code_by_name(db: AsyncSession, bank_name: str) -> str:
        """
        根据银行名称从数据库获取银行代码

        :param db: 数据库会话
        :param bank_name: 银行名称
        :return: 银行代码
        """
        if not bank_name:
            return ''

        try:
            from sqlalchemy import select, text

            # 先尝试精确匹配
            query = text("SELECT bank_code FROM bank_info WHERE bank_name = :bank_name AND status = 1")
            result = await db.execute(query, {"bank_name": bank_name})
            bank_code = result.scalar_one_or_none()

            if bank_code:
                return bank_code

            # 如果精确匹配失败，尝试模糊匹配
            query = text("SELECT bank_code FROM bank_info WHERE bank_name LIKE :bank_name AND status = 1 LIMIT 1")
            result = await db.execute(query, {"bank_name": f"%{bank_name}%"})
            bank_code = result.scalar_one_or_none()

            if bank_code:
                return bank_code

            # 反向模糊匹配
            query = text("SELECT bank_code FROM bank_info WHERE :bank_name LIKE CONCAT('%', bank_name, '%') AND status = 1 LIMIT 1")
            result = await db.execute(query, {"bank_name": bank_name})
            bank_code = result.scalar_one_or_none()

            return bank_code or ''

        except Exception as e:
            logger.error(f"从数据库获取银行代码失败: {str(e)}")
            return ''

    @staticmethod
    async def _get_bank_code_by_name_async(db: AsyncSession, bank_name: str) -> str:
        """
        根据银行名称异步查询银行代码

        :param db: 数据库会话
        :param bank_name: 银行名称
        :return: 银行代码
        """
        if not bank_name:
            return ''

        try:
            from sqlalchemy import text

            # 先尝试精确匹配
            query = text("SELECT bank_code FROM bank_info WHERE bank_name = :bank_name AND status = 1")
            result = await db.execute(query, {"bank_name": bank_name})
            bank_code = result.scalar_one_or_none()

            if bank_code:
                return bank_code

            # 如果精确匹配失败，尝试模糊匹配
            query = text("SELECT bank_code FROM bank_info WHERE bank_name LIKE :bank_name AND status = 1 LIMIT 1")
            result = await db.execute(query, {"bank_name": f"%{bank_name}%"})
            bank_code = result.scalar_one_or_none()

            if bank_code:
                return bank_code

            logger.warning(f"未找到银行代码，银行名称: {bank_name}")
            return ''

        except Exception as e:
            logger.error(f"查询银行代码失败: {str(e)}")
            return ''

    @staticmethod
    def _get_static_bank_code_by_name(bank_name: str) -> str:
        """
        根据银行名称获取银行代码（静态映射）

        :param bank_name: 银行名称
        :return: 银行代码
        """
        if not bank_name:
            return ''

        # 银行名称对应的银行代码映射
        bank_name_mapping = {
            '工商银行': '************',
            '中国工商银行': '************',
            '农业银行': '************',
            '中国农业银行': '************',
            '中国银行': '************',
            '建设银行': '************',
            '中国建设银行': '************',
            '交通银行': '************',
            '中信银行': '************',
            '光大银行': '************',
            '中国光大银行': '************',
            '民生银行': '************',
            '中国民生银行': '************',
            '平安银行': '************',
            '招商银行': '************',
            '兴业银行': '************',
            '浦发银行': '************',
            '上海浦东发展银行': '************',
            '华夏银行': '************',
            '广发银行': '************',
            '北京银行': '************',
            '渤海银行': '************',
            '邮储银行': '************',
            '上海银行': '************',
            '恒丰银行': '************',
            '广州银行': '************'
        }

        # 模糊匹配银行名称
        for name, code in bank_name_mapping.items():
            if name in bank_name or bank_name in name:
                return code

        return ''

    @staticmethod
    async def get_application_status(db: AsyncSession, mobile: str) -> AccountApplicationStatusVO:
        """
        获取开户申请状态
        
        :param db: 数据库会话
        :param mobile: 手机号
        :return: 申请状态
        """
        try:
            application = await AccountApplicationDao.get_application_by_mobile(db, mobile)
            
            if not application:
                return AccountApplicationStatusVO(
                    has_application=False,
                    status=None,
                    status_name=None,
                    apply_time=None,
                    review_time=None,
                    remark=None
                )
            
            # 状态映射
            status_map = {
                '0': '待审核',
                '1': '审核中',
                '2': '审核通过',
                '3': '审核拒绝'
            }
            
            return AccountApplicationStatusVO(
                has_application=True,
                status=application.status,
                status_name=status_map.get(application.status, '未知'),
                apply_time=application.apply_time.strftime('%Y-%m-%d %H:%M:%S') if application.apply_time else None,
                review_time=application.review_time.strftime('%Y-%m-%d %H:%M:%S') if application.review_time else None,
                remark=application.remark
            )
            
        except Exception as e:
            logger.error(f"获取申请状态失败: {str(e)}")
            raise QueryException(message=f"获取申请状态失败: {str(e)}")

    @staticmethod
    async def get_application_detail(db: AsyncSession, application_uuid: str) -> Optional[AccountApplicationDetailVO]:
        """
        获取开户申请详情
        
        :param db: 数据库会话
        :param application_uuid: 申请UUID
        :return: 申请详情
        """
        try:
            application = await AccountApplicationDao.get_application_by_uuid(db, application_uuid)
            
            if not application:
                return None
            
            # 状态映射
            status_map = {
                '0': '待审核',
                '1': '审核中',
                '2': '审核通过',
                '3': '审核拒绝'
            }
            
            gender_map = {
                '1': '男',
                '2': '女'
            }
            
            # 构建完整地址
            full_address = ""
            if application.province:
                full_address += application.province
            if application.city:
                full_address += application.city
            if application.district:
                full_address += application.district
            if application.address:
                full_address += application.address
            
            return AccountApplicationDetailVO(
                uuid=application.uuid,
                mobile=application.mobile,
                name=application.name,
                id_number=application.id_number,
                gender=application.gender,
                gender_name=gender_map.get(application.gender, '未知') if application.gender else None,
                birthday=application.birthday,
                province=application.province,
                city=application.city,
                district=application.district,
                address=application.address,
                full_address=full_address if full_address else None,
                occupation=application.occupation,
                company_name=application.company_name,
                annual_income=application.annual_income,
                bank_name=application.bank_name,
                bank_code=application.bank_code,
                account_no=application.account_no,
                account_name=application.account_name,
                id_card_front_url=application.id_card_front_url,
                id_card_back_url=application.id_card_back_url,
                bank_card_url=application.bank_card_url,
                issuing_authority=application.issuing_authority,
                validity_period=application.validity_period,
                yeepay_customer_code=application.yeepay_customer_code,
                status=application.status,
                status_name=status_map.get(application.status, '未知'),
                remark=application.remark,
                apply_time=application.apply_time.strftime('%Y-%m-%d %H:%M:%S') if application.apply_time else None,
                review_time=application.review_time.strftime('%Y-%m-%d %H:%M:%S') if application.review_time else None,
                reviewer_name=None  # TODO: 根据reviewer_id查询审核人姓名
            )
            
        except Exception as e:
            logger.error(f"获取申请详情失败: {str(e)}")
            raise QueryException(message=f"获取申请详情失败: {str(e)}")

    @staticmethod
    async def get_application_list(
        db: AsyncSession,
        page: int = 1,
        page_size: int = 20,
        status: Optional[str] = None,
        mobile: Optional[str] = None,
        name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取开户申请列表
        
        :param db: 数据库会话
        :param page: 页码
        :param page_size: 每页数量
        :param status: 申请状态
        :param mobile: 手机号
        :param name: 姓名
        :return: 申请列表
        """
        try:
            result = await AccountApplicationDao.get_application_list(
                db, page, page_size, status, mobile, name
            )
            
            # 状态映射
            status_map = {
                '0': '待审核',
                '1': '审核中',
                '2': '审核通过',
                '3': '审核拒绝'
            }
            
            # 转换为VO
            application_list = []
            for app in result["list"]:
                application_list.append(AccountApplicationListVO(
                    uuid=app.uuid,
                    mobile=app.mobile,
                    name=app.name,
                    id_number=app.id_number,
                    status=app.status,
                    status_name=status_map.get(app.status, '未知'),
                    apply_time=app.apply_time.strftime('%Y-%m-%d %H:%M:%S') if app.apply_time else None,
                    review_time=app.review_time.strftime('%Y-%m-%d %H:%M:%S') if app.review_time else None,
                    reviewer_name=None  # TODO: 根据reviewer_id查询审核人姓名
                ))
            
            return {
                "list": application_list,
                "total": result["total"],
                "page": result["page"],
                "page_size": result["page_size"]
            }
            
        except Exception as e:
            logger.error(f"获取申请列表失败: {str(e)}")
            raise QueryException(message=f"获取申请列表失败: {str(e)}")

    @staticmethod
    async def review_application(
        db: AsyncSession,
        review_data: AccountApplicationReviewVO,
        reviewer_id: str
    ) -> Dict[str, Any]:
        """
        审核开户申请
        
        :param db: 数据库会话
        :param review_data: 审核数据
        :param reviewer_id: 审核人ID
        :return: 审核结果
        """
        try:
            # 验证状态
            if review_data.status not in ['2', '3']:
                raise ValidationException(message="审核状态无效")
            
            # 更新申请状态
            success = await AccountApplicationDao.update_application_status(
                db, review_data.uuid, review_data.status, reviewer_id, review_data.remark
            )
            
            if not success:
                raise QueryException(message="审核失败")
            
            status_text = "通过" if review_data.status == '2' else "拒绝"
            logger.info(f"审核人 {reviewer_id} 审核申请 {review_data.uuid} {status_text}")
            
            return {
                "message": f"审核{status_text}成功"
            }
            
        except ValidationException:
            raise
        except Exception as e:
            logger.error(f"审核申请失败: {str(e)}")
            raise QueryException(message=f"审核申请失败: {str(e)}")

    @staticmethod
    async def _recognize_id_card_front(image_url: str) -> Dict[str, Any]:
        """
        识别身份证正面信息（OCR）

        :param image_url: 身份证正面照片URL
        :return: 识别结果
        """
        try:
            logger.info(f"识别身份证正面信息: {image_url}")

            # 调用百度OCR识别
            result = await baidu_ocr_util.recognize_id_card_front_by_url(image_url)

            if result:
                logger.info("身份证正面识别成功")
                return result
            else:
                logger.error("身份证正面识别失败")
                return {}

        except Exception as e:
            logger.error(f"身份证正面识别失败: {str(e)}")
            return {}

    @staticmethod
    async def _recognize_id_card_back(image_url: str) -> Dict[str, Any]:
        """
        识别身份证背面信息（OCR）

        :param image_url: 身份证背面照片URL
        :return: 识别结果
        """
        try:
            logger.info(f"识别身份证背面信息: {image_url}")

            # 调用百度OCR识别
            result = await baidu_ocr_util.recognize_id_card_back_by_url(image_url)

            if result:
                logger.info("身份证背面识别成功")
                return result
            else:
                logger.error("身份证背面识别失败")
                return {}

        except Exception as e:
            logger.error(f"身份证背面识别失败: {str(e)}")
            return {}

    @staticmethod
    async def _recognize_bank_card(image_url: str) -> Dict[str, Any]:
        """
        识别银行卡信息（OCR）

        :param image_url: 银行卡照片URL
        :return: 识别结果
        """
        try:
            logger.info(f"识别银行卡信息: {image_url}")

            # 调用百度OCR识别
            result = await baidu_ocr_util.recognize_bank_card_by_url(image_url)

            if result:
                logger.info("银行卡识别成功")
                return result
            else:
                logger.error("银行卡识别失败")
                return {}

        except Exception as e:
            logger.error(f"银行卡识别失败: {str(e)}")
            return {}

    @staticmethod
    def _parse_ocr_data(application_data: AccountApplicationSubmitVO) -> tuple:
        """
        解析前端传递的OCR识别结果

        :param application_data: 申请数据
        :return: (id_card_info, bank_card_info) 元组
        """
        id_card_info = {}
        bank_card_info = {}

        # 解析身份证正面OCR结果
        if application_data.id_card_front_ocr:
            logger.info("解析前端传递的身份证正面OCR结果")
            ocr_data = application_data.id_card_front_ocr

            # 提取身份证正面信息
            id_card_info.update({
                'name': ocr_data.name or '',
                'id_number': ocr_data.id_number or '',
                'gender': ocr_data.gender or '',
                'birthday': ocr_data.birthday or '',
                'address': ocr_data.address or '',
                'province': ocr_data.province or '',
                'city': ocr_data.city or '',
                'district': ocr_data.district or ''
            })

            logger.info(f"身份证正面OCR数据: 姓名={ocr_data.name}, 身份证号={ocr_data.id_number}, 性别={ocr_data.gender}")
        else:
            logger.info("未提供身份证正面OCR结果")
            id_card_info.update({
                'name': '',
                'id_number': '',
                'gender': '',
                'birthday': '',
                'address': '',
                'province': '',
                'city': '',
                'district': ''
            })

        # 解析身份证背面OCR结果
        if application_data.id_card_back_ocr:
            logger.info("解析前端传递的身份证背面OCR结果")
            ocr_data = application_data.id_card_back_ocr

            # 提取身份证背面信息
            id_card_info.update({
                'issuing_authority': ocr_data.issue_authority or '',
                'validity_period': ocr_data.valid_period or ''
            })

            logger.info(f"身份证背面OCR数据: 签发机关={ocr_data.issue_authority}, 有效期={ocr_data.valid_period}")
        else:
            logger.info("未提供身份证背面OCR结果")
            id_card_info.update({
                'issuing_authority': '',
                'validity_period': ''
            })

        # 解析银行卡OCR结果
        if application_data.bank_card_ocr:
            logger.info("解析前端传递的银行卡OCR结果")
            ocr_data = application_data.bank_card_ocr

            # 提取银行卡信息
            bank_card_info.update({
                'bank_name': ocr_data.bank_name or '',
                'account_no': (ocr_data.card_number or '').replace(' ', ''),  # 去除所有空格
                'account_name': ocr_data.card_holder or '',
                'card_type': ocr_data.card_type or ''
            })

            logger.info(f"银行卡OCR数据: 银行={ocr_data.bank_name}, 卡号={ocr_data.card_number}, 持卡人={ocr_data.card_holder}")
        else:
            logger.info("未提供银行卡OCR结果")
            bank_card_info.update({
                'bank_name': '',
                'account_no': '',
                'account_name': '',
                'card_type': ''
            })

        # 记录解析结果摘要
        logger.info(f"OCR数据解析完成:")
        logger.info(f"  身份证信息: 姓名={id_card_info.get('name')}, 身份证号={id_card_info.get('id_number')}")
        logger.info(f"  银行卡信息: 银行={bank_card_info.get('bank_name')}, 卡号={bank_card_info.get('account_no')}")

        return id_card_info, bank_card_info

    @staticmethod
    async def _compress_image(image_data: bytes, max_size_mb: float = 1.8) -> bytes:
        """
        压缩图片确保文件大小小于指定大小

        :param image_data: 原始图片数据
        :param max_size_mb: 最大文件大小（MB）
        :return: 压缩后的图片数据
        """
        try:
            from PIL import Image
            import io

            max_size_bytes = int(max_size_mb * 1024 * 1024)

            # 计算base64编码后的大小（约为原始大小的4/3）
            base64_size = len(image_data) * 4 // 3

            # 如果base64编码后的大小小于限制，直接返回
            if base64_size <= max_size_bytes:
                logger.info(f"图片大小 {len(image_data)} 字节，base64编码后约 {base64_size} 字节，无需压缩")
                return image_data

            logger.info(f"图片大小 {len(image_data)} 字节，base64编码后约 {base64_size} 字节，需要压缩到 {max_size_bytes} 字节以下")

            # 打开图片
            image = Image.open(io.BytesIO(image_data))

            # 转换为RGB模式（如果是RGBA等其他模式）
            if image.mode in ('RGBA', 'LA', 'P'):
                # 创建白色背景
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                image = background
            elif image.mode != 'RGB':
                image = image.convert('RGB')

            # 获取原始尺寸
            original_width, original_height = image.size
            logger.info(f"原始图片尺寸: {original_width}x{original_height}")

            # 尝试不同的压缩质量和尺寸
            quality = 85
            scale_factor = 1.0

            while quality >= 20 or scale_factor > 0.3:
                # 调整图片尺寸
                if scale_factor < 1.0:
                    new_width = int(original_width * scale_factor)
                    new_height = int(original_height * scale_factor)
                    resized_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
                    logger.info(f"调整图片尺寸到: {new_width}x{new_height}")
                else:
                    resized_image = image

                # 压缩图片
                output = io.BytesIO()
                resized_image.save(output, format='JPEG', quality=quality, optimize=True)
                compressed_data = output.getvalue()

                # 计算base64编码后的大小
                compressed_base64_size = len(compressed_data) * 4 // 3
                logger.info(f"压缩质量 {quality}，缩放比例 {scale_factor:.2f}，压缩后大小: {len(compressed_data)} 字节，base64编码后约: {compressed_base64_size} 字节")

                # 检查是否满足大小要求（考虑base64编码）
                if compressed_base64_size <= max_size_bytes:
                    logger.info(f"压缩成功，最终大小: {len(compressed_data)} 字节，base64编码后约: {compressed_base64_size} 字节")
                    return compressed_data

                # 调整参数
                if quality > 20:
                    quality -= 10
                else:
                    scale_factor -= 0.1
                    quality = 85  # 重置质量

            # 如果仍然太大，使用最小设置
            logger.warning("使用最小压缩设置")
            final_width = int(original_width * 0.3)
            final_height = int(original_height * 0.3)
            final_image = image.resize((final_width, final_height), Image.Resampling.LANCZOS)

            output = io.BytesIO()
            final_image.save(output, format='JPEG', quality=20, optimize=True)
            final_data = output.getvalue()

            final_base64_size = len(final_data) * 4 // 3
            logger.info(f"最终压缩结果大小: {len(final_data)} 字节，base64编码后约: {final_base64_size} 字节")
            return final_data

        except Exception as e:
            logger.error(f"图片压缩失败: {str(e)}")
            # 如果压缩失败，返回原始数据（可能会导致上传失败，但至少不会崩溃）
            return image_data

    @staticmethod
    async def _upload_files_to_yeepay(application_dict: Dict[str, Any]) -> Dict[str, str]:
        """
        上传文件到易宝并获取文件ID

        :param application_dict: 申请数据字典
        :return: 文件ID字典
        """
        try:
            import base64
            import httpx

            logger.info("开始上传文件到易宝")

            # 获取易宝配置
            yeepay_settings = YeepaySettings()

            file_ids = {}

            # 需要上传的文件列表
            files_to_upload = [
                ("id_card_front", application_dict.get("id_card_front_url", ""), "id_card_front.jpg"),
                ("id_card_back", application_dict.get("id_card_back_url", ""), "id_card_back.jpg"),
                ("bank_card", application_dict.get("bank_card_url", ""), "bank_card.jpg")
            ]

            for file_key, file_url, file_name in files_to_upload:
                if not file_url:
                    logger.warning(f"文件URL为空，跳过上传: {file_key}")
                    continue

                try:
                    # 下载文件内容
                    logger.info(f"下载文件: {file_url}")
                    async with httpx.AsyncClient(timeout=30) as client:
                        response = await client.get(file_url)
                        if response.status_code != 200:
                            logger.error(f"下载文件失败，状态码: {response.status_code}, URL: {file_url}")
                            continue

                        file_content = response.content

                    # 压缩图片确保小于2M
                    compressed_content = await AccountApplicationService._compress_image(file_content, max_size_mb=1.8)

                    # 将文件内容转换为base64
                    file_base64 = base64.b64encode(compressed_content).decode('utf-8')

                    # 构建上传请求
                    now = datetime.now()
                    req_time = now.strftime("%Y%m%d%H%M%S")
                    trace_no = f"UPLOAD{req_time}{str(uuid.uuid4()).replace('-', '')[:6]}"

                    request_data = {
                        "msgPublic": {
                            "version": yeepay_settings.YEEPAY_VERSION,
                            "cusReqTime": req_time,
                            "cusTraceNo": trace_no,
                            "cusCode": yeepay_settings.YEEPAY_CUSTOMER_CODE
                        },
                        "msgPrivate": {
                            "fileName": file_name,
                            "fileData": file_base64
                        }
                    }

                    # 构建完整请求体
                    request_body = {"body": request_data}

                    # 生成签名
                    merchant_private_key = yeepay_settings.YEEPAY_MERCHANT_PRIVATE_KEY
                    if not merchant_private_key:
                        raise BusinessException(message="易宝支付商户私钥未配置")

                    # 将body转换为JSON字符串进行签名
                    body_json = json.dumps(request_body["body"], ensure_ascii=False, separators=(',', ':'), sort_keys=False)
                    # logger.info(f"文件上传签名内容: {body_json}")

                    # 生成签名（复用支付服务的签名方法）
                    from module_admin.service.payment_service import PaymentService
                    signature = PaymentService._generate_rsa_signature(body_json, merchant_private_key)
                    logger.info(f"文件上传生成的签名: {signature}")

                    # 添加签名到请求体
                    request_body["sign"] = signature

                    # 调用易宝文件上传API
                    api_url = "https://api.kuaijie-pay.com/forward/innet/txn/v2/cus/innet/upload"
                    logger.info(f"准备调用易宝文件上传API: {api_url}")

                    async with httpx.AsyncClient(timeout=yeepay_settings.YEEPAY_TIMEOUT) as client:
                        response = await client.post(
                            api_url,
                            json=request_body,
                            headers={'Content-Type': 'application/json'}
                        )

                        logger.info(f"文件上传API HTTP状态码: {response.status_code}")

                        if response.status_code != 200:
                            logger.error(f"文件上传API HTTP请求失败，状态码: {response.status_code}")
                            continue

                        response_data = response.json()
                        logger.info(f"文件上传API响应: {response_data}")

                        # 解析响应
                        if isinstance(response_data, dict) and "body" in response_data:
                            body_data = json.loads(response_data["body"]) if isinstance(response_data["body"], str) else response_data["body"]
                        else:
                            body_data = response_data

                        # 检查上传结果
                        msg_public = body_data.get("msgPublic", {})
                        if msg_public.get("rspCode") == "0000":
                            msg_private = body_data.get("msgPrivate", {})
                            file_id = msg_private.get("fileId")
                            if file_id:
                                file_ids[file_key] = file_id
                                logger.info(f"文件上传成功: {file_key} -> {file_id}")
                            else:
                                logger.error(f"文件上传响应中未找到fileId: {file_key}")
                        else:
                            error_msg = msg_public.get("rspMsg", "文件上传失败")
                            logger.error(f"文件上传失败: {file_key}, 错误: {error_msg}")

                except Exception as e:
                    logger.error(f"上传文件异常: {file_key}, 错误: {str(e)}")
                    continue

            logger.info(f"文件上传完成，获得文件ID: {file_ids}")
            return file_ids

        except Exception as e:
            logger.error(f"上传文件到易宝异常: {str(e)}")
            return {}

    @staticmethod
    async def _call_yeepay_innet_api(application_dict: Dict[str, Any], mobile: str) -> Dict[str, Any]:
        """
        调用易宝开户接口

        :param application_dict: 申请数据字典
        :param mobile: 手机号
        :return: 易宝接口响应
        """
        try:
            logger.info("=== 开始调用易宝开户接口 ===")

            # 先上传文件获取文件ID
            logger.info("开始上传文件到易宝")
            file_ids = await AccountApplicationService._upload_files_to_yeepay(application_dict)

            # 检查必要的文件是否上传成功
            required_files = ["id_card_front", "id_card_back", "bank_card"]
            missing_files = [f for f in required_files if not file_ids.get(f)]

            if missing_files:
                logger.error(f"必要文件上传失败: {missing_files}")
                raise BusinessException(message=f"文件上传失败，缺少文件: {', '.join(missing_files)}")

            # 获取易宝配置
            yeepay_settings = YeepaySettings()

            # 生成请求参数
            now = datetime.now()
            req_time = now.strftime("%Y%m%d%H%M%S")
            trace_no = f"INNET{req_time}{str(uuid.uuid4()).replace('-', '')[:6]}"
            # 构建公有参数
            msg_public = {
                "version": yeepay_settings.YEEPAY_VERSION,
                "cusReqTime": req_time,
                "cusTraceNo": trace_no,
                "cusCode": yeepay_settings.YEEPAY_CUSTOMER_CODE
            }

            # 构建私有参数 - 入网主体信息
            innet_owner = {
                "cifName": application_dict.get("name", ""),  # 主体名称
                "cifBriefName": application_dict.get("name", "")[:16] if application_dict.get("name") else "",  # 主体简称，限制16个字符
            }

            # 构建法人信息
            innet_juridical = {
                "juridicalName": application_dict.get("name", ""),  # 法人姓名
                "juridicalCertType": "P01",  # 法人证件类型：身份证
                "juridicalCertNo": application_dict.get("id_number", ""),  # 法人证件号码
                "juridicalCertFront": file_ids.get("id_card_front", ""),  # 法人证件人像面文件ID
                "juridicalCertBack": file_ids.get("id_card_back", ""),  # 法人证件非人像面文件ID
                "juridicalPhone": mobile,  # 法人手机号
            }

            # 构建经营地址信息
            innet_address = {
                "address": application_dict.get("address", ""),  # 详细地址
                "province": application_dict.get("province_code", ""),  # 省份代码
                "city": application_dict.get("city_code", ""),  # 城市代码
                "district": application_dict.get("district_code", ""),  # 区县代码
            }

            # 构建结算账户信息（对象格式，非数组）
            innet_account = {
                "bankCode": application_dict.get("bank_code", ""),  # 银行代码
                "bankAcctType": "pCard",  # 账户类型：pCard-个人借记卡
                "bankAcctNo": application_dict.get("account_no", ""),  # 账户号码
                "bankAcctFront": file_ids.get("bank_card", ""),  # 银行卡正面文件ID
                "bankAcctBack": file_ids.get("bank_card", ""),  # 银行卡背面文件ID（使用同一张银行卡图片）
            }

            # 构建产品信息
            product = [{
                "productCode": "SettWithdraw",  # 产品代码：结算提现
                "listFee": [{
                    "cfgValue": "all",  # 配置值
                    "feeValue": 0,  # 费率值
                    "feeType": "2"  # 费率类型
                }]
            }]

            # 构建私有参数体
            msg_private = {
                "innetOwner": innet_owner,
                "innetJuridical": innet_juridical,
                "innetAddress": innet_address,
                "innetAccount": innet_account,
                "product": product
            }
            
            # 构建完整请求参数
            request_data = {
                "msgPublic": msg_public,
                "msgPrivate": msg_private
            }

            logger.info(f"易宝开户请求参数: {json.dumps(request_data, ensure_ascii=False, indent=2)}")

            # 构建完整请求体
            request_body = {"body": request_data}

            # 生成签名
            merchant_private_key = yeepay_settings.YEEPAY_MERCHANT_PRIVATE_KEY
            if not merchant_private_key:
                raise BusinessException(message="易宝支付商户私钥未配置")

            # 将body转换为JSON字符串进行签名（与payment_service保持一致）
            body_json = json.dumps(request_body["body"], ensure_ascii=False, separators=(',', ':'), sort_keys=False)
            logger.info(f"易宝开户签名内容: {body_json}")

            # 生成签名（复用支付服务的签名方法）
            signature = PaymentService._generate_rsa_signature(body_json, merchant_private_key)
            logger.info(f"易宝开户生成的签名: {signature}")

            # 添加签名到请求体
            request_body["sign"] = signature
            logger.info(f"易宝开户完整请求体: {request_body}")

            # 调用易宝开户API
            api_url = "https://api.kuaijie-pay.com/forward/innet/txn/v2/cus/innet/micro"
            logger.info(f"准备调用易宝开户API: {api_url}")
            logger.info(f"请求超时设置: {yeepay_settings.YEEPAY_TIMEOUT}")

            async with httpx.AsyncClient(timeout=yeepay_settings.YEEPAY_TIMEOUT) as client:
                logger.info("开始发送HTTP POST请求...")
                
                # 记录实际发送的JSON字符串（与payment_service保持一致）
                actual_json = json.dumps(request_body, ensure_ascii=False, separators=(',', ':'), sort_keys=False)
                logger.info(f"实际发送的JSON字符串: {actual_json}")
                logger.info(f"JSON字符串长度: {len(actual_json)}")
                logger.info(f"JSON字符串前10个字符: {repr(actual_json[:10])}")
                
                try:
                    response = await client.post(
                        api_url,
                        json=request_body,
                        headers={"Content-Type": "application/json"}
                    )
                    logger.info("HTTP请求发送成功")
                except Exception as http_error:
                    logger.error(f"HTTP请求发送失败: {str(http_error)}")
                    logger.error(f"HTTP错误类型: {type(http_error).__name__}")
                    logger.error(f"HTTP错误详情: {repr(http_error)}")
                    raise

                logger.info(f"易宝开户API HTTP状态码: {response.status_code}")
                logger.info(f"易宝开户API响应头: {dict(response.headers)}")

                if response.status_code != 200:
                    raise BusinessException(message=f"易宝开户API请求失败，状态码: {response.status_code}")

                # 解析响应
                response_data = response.json()
                logger.info(f"易宝开户API原始响应: {response_data}")

                # 提取body部分 - 处理易宝API的响应格式
                if "body" in response_data:
                    body_str = response_data["body"]
                    if isinstance(body_str, str):
                        # body是JSON字符串，需要解析
                        body_data = json.loads(body_str)
                    else:
                        # body已经是字典
                        body_data = body_str

                    logger.info(f"解析后的body数据: {body_data}")

                    # 根据易宝API响应格式提取结果
                    # 优先尝试嵌套结构，然后尝试扁平化结构
                    if "msgPublic" in body_data:
                        # 嵌套结构响应 - 返回完整的原始数据结构
                        msg_public = body_data["msgPublic"]
                        msg_private = body_data.get("msgPrivate", {})

                        # 返回完整的原始数据，包含所有字段
                        result = {
                            "rspCode": msg_public.get("rspCode", ""),
                            "rspMsg": msg_public.get("rspMsg", ""),
                            "subCusCode": msg_private.get("subCusCode", "") or msg_public.get("subCusCode", ""),
                            "rspRemark": msg_private.get("rspRemark", "") or msg_public.get("rspRemark", ""),
                            # 添加关键的流水号和日期信息
                            "msgPublic": msg_public,
                            "msgPrivate": msg_private
                        }
                    else:
                        # 扁平化结构响应
                        result = {
                            "rspCode": body_data.get("rspCode", ""),
                            "rspMsg": body_data.get("rspMsg", ""),
                            "subCusCode": body_data.get("subCusCode", ""),
                            "rspRemark": body_data.get("rspRemark", ""),
                            # 保留原始数据
                            **body_data
                        }

                    logger.info(f"易宝开户结果: {result}")
                    return result
                else:
                    logger.error("易宝开户API响应格式异常，缺少body字段")
                    raise BusinessException(message="易宝开户API响应格式异常")

        except httpx.TimeoutException as e:
            logger.error(f"易宝开户API请求超时: {str(e)}")
            logger.error(f"超时异常详情: {type(e).__name__}")
            raise BusinessException(message="易宝开户API请求超时")
        except httpx.RequestError as e:
            logger.error(f"易宝开户API请求异常: {str(e)}")
            logger.error(f"请求异常类型: {type(e).__name__}")
            logger.error(f"请求异常详情: {repr(e)}")
            raise BusinessException(message=f"易宝开户API请求异常: {str(e)}")
        except json.JSONDecodeError as e:
            logger.error(f"易宝开户API响应JSON解析失败: {str(e)}")
            logger.error(f"响应内容: {response.text if 'response' in locals() else 'N/A'}")
            raise BusinessException(message=f"易宝开户API响应格式错误: {str(e)}")
        except Exception as e:
            logger.error(f"调用易宝开户接口失败: {str(e)}")
            logger.error(f"异常类型: {type(e).__name__}")
            logger.error(f"异常详情: {repr(e)}")
            if 'response' in locals():
                logger.error(f"HTTP响应状态码: {response.status_code}")
                logger.error(f"HTTP响应内容: {response.text}")
            raise BusinessException(message=f"调用易宝开户接口失败: {str(e)}")

    @staticmethod
    async def handle_yeepay_innet_notify_service(
        query_db: AsyncSession,
        notify_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理易宝开户异步通知服务

        Args:
            query_db: 数据库会话
            notify_data: 易宝开户通知数据

        Returns:
            处理结果
        """
        try:
            logger.info("=== 开始处理易宝开户异步通知 ===")

            # 验证通知数据格式
            if not isinstance(notify_data, dict):
                raise ValidationException(message="通知数据格式错误")

            # 验证签名（如果有配置公钥）
            notify_sign = notify_data.get("sign")
            notify_body = notify_data.get("body")

            if notify_sign and notify_body:
                yeepay_settings = YeepaySettings()
                platform_public_key = yeepay_settings.YEEPAY_PLATFORM_PUBLIC_KEY
                if platform_public_key:
                    # 验证通知签名
                    body_content = notify_body if isinstance(notify_body, str) else json.dumps(notify_body, ensure_ascii=False, separators=(',', ':'))
                    from module_admin.service.payment_service import PaymentService
                    is_valid = PaymentService._verify_rsa_signature(body_content, notify_sign, platform_public_key)
                    if not is_valid:
                        logger.error("易宝开户通知签名验证失败")
                        raise BusinessException(message="通知签名验证失败")
                    logger.info("✅ 易宝开户通知签名验证成功")
                else:
                    logger.warning("⚠️ 未配置平台公钥，跳过通知签名验证")

            # 解析通知内容
            if isinstance(notify_body, str):
                body_data = json.loads(notify_body)
            else:
                body_data = notify_body

            logger.info(f"解析后的通知内容: {body_data}")

            # 获取公共参数
            msg_public = body_data.get("msgPublic", {})
            msg_private = body_data.get("msgPrivate", {})

            # 提取关键信息
            cus_trace_no = msg_public.get("cusTraceNo", "")  # 客户请求流水号
            sys_trace_no = msg_public.get("sysTraceNo", "")  # 系统跟踪号
            rsp_code = msg_public.get("rspCode", "")  # 应答码
            rsp_msg = msg_public.get("rspMsg", "")  # 应答信息

            # 私有参数
            txn_state = msg_private.get("txnState", "")  # 原交易状态

            logger.info(f"通知关键信息: cusTraceNo={cus_trace_no}, rspCode={rsp_code}, txnState={txn_state}")

            # 根据客户请求流水号查找对应的开户申请
            if cus_trace_no:
                # 从流水号中提取手机号或其他标识信息
                # 这里需要根据实际的流水号生成规则来解析
                application = await AccountApplicationDao.get_application_by_trace_no(query_db, cus_trace_no)

                if application:
                    # 更新申请状态
                    status_mapping = {
                        "0": "0",  # 待处理 -> 待审核
                        "1": "2",  # 成功 -> 审核通过
                        "2": "1",  # 处理中 -> 审核中
                        "4": "3",  # 失败 -> 审核拒绝
                        "8": "3"   # 取消 -> 审核拒绝
                    }

                    new_status = status_mapping.get(txn_state, "0")

                    # 从msgPrivate中获取subCusCode（入网客户代码）
                    sub_cus_code = msg_private.get("subCusCode", "")

                    # 构建更新数据
                    update_data = {
                        "status": new_status,
                        "remark": f"易宝通知: {rsp_msg}",
                        "yeepay_customer_code": sub_cus_code
                    }

                    # 记录详细的通知信息
                    if sub_cus_code:
                        logger.info(f"获得易宝客户代码: {sub_cus_code}")

                    # 根据rspCode判断是否成功
                    if rsp_code == "0000":
                        logger.info(f"易宝开户处理成功: {rsp_msg}")
                    else:
                        logger.warning(f"易宝开户处理异常: {rsp_code} - {rsp_msg}")

                    # 更新申请记录
                    await AccountApplicationDao.update_application_status_by_notify(
                        query_db, application.uuid, update_data
                    )

                    logger.info(f"已更新申请状态: {application.uuid} -> {new_status}")

                    return {
                        "status": "success",
                        "message": "通知处理成功",
                        "application_uuid": application.uuid,
                        "new_status": new_status
                    }
                else:
                    logger.warning(f"未找到对应的开户申请: cusTraceNo={cus_trace_no}")
                    return {
                        "status": "warning",
                        "message": "未找到对应的开户申请"
                    }
            else:
                logger.error("通知中缺少客户请求流水号")
                return {
                    "status": "error",
                    "message": "通知中缺少客户请求流水号"
                }

        except BusinessException as e:
            logger.error(f"处理易宝开户通知业务异常: {str(e)}")
            raise e
        except ValidationException as e:
            logger.error(f"处理易宝开户通知验证异常: {str(e)}")
            raise e
        except Exception as e:
            logger.error(f"处理易宝开户通知系统异常: {str(e)}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            raise BusinessException(message=f"处理开户通知失败: {str(e)}")

    @staticmethod
    async def query_yeepay_innet_result_service(
        query_db: AsyncSession,
        application_uuid: str
    ) -> Dict[str, Any]:
        """查询易宝入网结果服务

        Args:
            query_db: 数据库会话
            application_uuid: 申请UUID

        Returns:
            查询结果
        """
        try:
            logger.info(f"=== 开始查询易宝入网结果 ===")
            logger.info(f"请求参数 - 申请UUID: {application_uuid}")

            # 获取申请记录
            logger.info(f"步骤1: 查询申请记录")
            application = await AccountApplicationDao.get_application_by_uuid(query_db, application_uuid)
            if not application:
                logger.error(f"申请记录不存在: {application_uuid}")
                raise BusinessException(message="申请记录不存在")

            logger.info(f"申请记录查询成功:")
            logger.info(f"  - 手机号: {application.mobile}")
            logger.info(f"  - 姓名: {application.name}")
            logger.info(f"  - 申请时间: {application.apply_time}")
            logger.info(f"  - 当前状态: {application.status}")

            # 检查是否有易宝相关信息
            logger.info(f"步骤2: 检查易宝相关信息")
            logger.info(f"  - 易宝客户代码: {application.yeepay_customer_code}")
            logger.info(f"  - 易宝客户流水号: {application.yeepay_cus_trace_no}")
            logger.info(f"  - 易宝系统跟踪号: {application.yeepay_sys_trace_no}")
            logger.info(f"  - 易宝交易日期: {application.yeepay_txn_date}")
            logger.info(f"  - 易宝响应时间: {application.yeepay_rsp_time}")

            if not application.yeepay_cus_trace_no and not application.yeepay_sys_trace_no:
                logger.error(f"该申请未调用易宝开户接口，缺少流水号信息")
                raise BusinessException(message="该申请未调用易宝开户接口")

            # 构建查询参数
            logger.info(f"步骤3: 构建易宝查询参数")
            orig_txn_date = application.yeepay_txn_date
            orig_cus_trace_no = application.yeepay_cus_trace_no
            orig_sys_trace_no = application.yeepay_sys_trace_no

            if not orig_txn_date:
                # 如果没有存储交易日期，使用申请时间的日期
                if application.apply_time:
                    orig_txn_date = application.apply_time.strftime("%Y%m%d")
                    logger.info(f"交易日期为空，使用申请时间生成: {orig_txn_date}")
                else:
                    logger.error(f"无法确定交易日期，申请时间也为空")
                    raise BusinessException(message="无法确定交易日期")

            logger.info(f"易宝查询参数构建完成:")
            logger.info(f"  - origTxnDate: {orig_txn_date}")
            logger.info(f"  - origCusTraceNo: {orig_cus_trace_no}")
            logger.info(f"  - origSysTraceNo: {orig_sys_trace_no}")

            # 调用易宝查询接口
            logger.info(f"步骤4: 调用易宝查询接口")
            logger.info(f"准备调用 YeepayInnetService.query_innet_result_service")

            from module_admin.service.yeepay_innet_service import YeepayInnetService

            logger.info(f"开始发送易宝查询请求...")
            yeepay_result = await YeepayInnetService.query_innet_result_service(
                query_db,
                orig_txn_date,
                orig_cus_trace_no,
                orig_sys_trace_no
            )

            logger.info(f"易宝查询接口调用完成")
            logger.info(f"易宝返回结果: {yeepay_result}")

            # 解析易宝返回结果
            logger.info(f"步骤5: 解析易宝返回结果")

            # 如果返回的是字符串，尝试解析为JSON
            if isinstance(yeepay_result, str):
                try:
                    import json
                    yeepay_result = json.loads(yeepay_result)
                    logger.info(f"字符串格式数据解析成功: {yeepay_result}")
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析失败: {str(e)}")
                    logger.error(f"原始数据: {yeepay_result}")

            if isinstance(yeepay_result, dict):
                # 从嵌套结构中提取信息
                msg_public = yeepay_result.get("msgPublic", {})
                msg_private = yeepay_result.get("msgPrivate", {})

                # 优先从msgPublic获取响应码和消息
                rsp_code = msg_public.get("rspCode") or yeepay_result.get("rspCode")
                rsp_msg = msg_public.get("rspMsg") or yeepay_result.get("rspMsg")

                # 从msgPrivate获取交易状态和客户代码
                txn_state = msg_private.get("txnState") or yeepay_result.get("txnState")
                sub_cus_code = msg_private.get("subCusCode") or yeepay_result.get("subCusCode")

                logger.info(f"易宝响应解析:")
                logger.info(f"  - rspCode: {rsp_code}")
                logger.info(f"  - rspMsg: {rsp_msg}")
                logger.info(f"  - txnState: {txn_state}")
                logger.info(f"  - subCusCode: {sub_cus_code}")

                if rsp_code == "0000":
                    logger.info(f"✅ 易宝查询成功")
                else:
                    logger.warning(f"⚠️ 易宝查询返回异常状态: {rsp_code} - {rsp_msg}")
            else:
                logger.warning(f"易宝返回结果格式异常: {type(yeepay_result)}")

            # 构建返回结果
            logger.info(f"步骤6: 构建返回结果")
            result = {
                "application_uuid": application_uuid,
                "yeepay_query_result": yeepay_result,
                "stored_info": {
                    "yeepay_customer_code": application.yeepay_customer_code,
                    "yeepay_cus_trace_no": application.yeepay_cus_trace_no,
                    "yeepay_sys_trace_no": application.yeepay_sys_trace_no,
                    "yeepay_txn_date": application.yeepay_txn_date,
                    "yeepay_rsp_time": application.yeepay_rsp_time
                }
            }

            logger.info(f"=== 查询易宝入网结果完成 ===")
            logger.info(f"最终返回结果: {result}")
            return result

        except BusinessException as e:
            logger.error(f"查询易宝入网结果业务异常: {e.message}")
            raise e
        except Exception as e:
            logger.error(f"查询易宝入网结果系统异常: {str(e)}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            raise BusinessException(message=f"查询失败: {str(e)}")

    @staticmethod
    async def check_account_status_by_mobile_service(
        query_db: AsyncSession,
        mobile: str
    ) -> Dict[str, Any]:
        """根据手机号检查开户状态服务

        Args:
            query_db: 数据库会话
            mobile: 手机号

        Returns:
            开户状态信息
        """
        try:
            logger.info(f"=== 检查开户状态 ===")
            logger.info(f"手机号: {mobile}")

            # 查询该手机号的最新开户申请
            application = await AccountApplicationDao.get_latest_application_by_mobile(query_db, mobile)

            if not application:
                # 没有开户记录，显示开户按钮
                logger.info(f"手机号 {mobile} 没有开户记录")
                return {
                    "has_application": False,
                    "need_apply": True,
                    "status": None,
                    "status_name": "未开户",
                    "message": "请先完成开户申请"
                }

            logger.info(f"找到开户申请: UUID={application.uuid}, 状态={application.status}")

            # 如果已经审核通过，直接返回
            if application.status == "2":
                logger.info(f"开户已审核通过")
                return {
                    "has_application": True,
                    "need_apply": False,
                    "status": "2",
                    "status_name": "审核通过",
                    "application_uuid": application.uuid,
                    "yeepay_customer_code": application.yeepay_customer_code,
                    "message": "开户已完成"
                }

            # 如果是审核中状态，需要查询易宝结果
            if application.status == "1":  # 审核中
                logger.info(f"当前状态为审核中，需要查询易宝结果")

                # 检查是否有易宝相关信息
                if application.yeepay_cus_trace_no or application.yeepay_sys_trace_no:
                    try:
                        logger.info(f"检测到易宝相关信息，开始查询易宝状态")
                        logger.info(f"  - 客户流水号: {application.yeepay_cus_trace_no}")
                        logger.info(f"  - 系统跟踪号: {application.yeepay_sys_trace_no}")

                        # 调用易宝查询接口
                        logger.info(f"调用易宝查询接口...")
                        yeepay_result = await AccountApplicationService.query_yeepay_innet_result_service(
                            query_db, application.uuid
                        )

                        logger.info(f"易宝查询接口调用完成，结果: {yeepay_result}")

                        # 解析易宝查询结果
                        yeepay_query_result = yeepay_result.get("yeepay_query_result", {})

                        # 如果yeepay_query_result是字符串，尝试解析为JSON
                        if isinstance(yeepay_query_result, str):
                            try:
                                import json
                                yeepay_query_result = json.loads(yeepay_query_result)
                                logger.info(f"易宝查询结果JSON解析成功: {yeepay_query_result}")
                            except json.JSONDecodeError as e:
                                logger.error(f"易宝查询结果JSON解析失败: {str(e)}")
                                yeepay_query_result = {}

                        # 从嵌套结构中提取信息
                        if isinstance(yeepay_query_result, dict):
                            msg_public = yeepay_query_result.get("msgPublic", {})
                            msg_private = yeepay_query_result.get("msgPrivate", {})

                            # 优先从msgPublic获取响应码和消息
                            rsp_code = msg_public.get("rspCode") or yeepay_query_result.get("rspCode")
                            rsp_msg = msg_public.get("rspMsg") or yeepay_query_result.get("rspMsg")

                            # 从msgPrivate获取交易状态和客户代码
                            txn_state = msg_private.get("txnState") or yeepay_query_result.get("txnState")
                            sub_cus_code = msg_private.get("subCusCode") or yeepay_query_result.get("subCusCode")
                        else:
                            rsp_code = None
                            rsp_msg = None
                            txn_state = None
                            sub_cus_code = None

                        logger.info(f"易宝查询结果解析:")
                        logger.info(f"  - rspCode: {rsp_code}")
                        logger.info(f"  - rspMsg: {rsp_msg}")
                        logger.info(f"  - txnState: {txn_state}")
                        logger.info(f"  - subCusCode: {sub_cus_code}")

                        # 如果易宝显示成功，更新本地状态
                        if txn_state == "1":  # 易宝显示成功
                            logger.info(f"✅ 易宝显示开户成功，准备更新本地状态")

                            # 更新申请状态为审核通过
                            update_data = {
                                "status": "2",
                                "remark": "易宝查询确认开户成功"
                            }

                            # 如果有新的客户代码，也更新
                            if sub_cus_code and sub_cus_code != application.yeepay_customer_code:
                                update_data["yeepay_customer_code"] = sub_cus_code
                                logger.info(f"更新客户代码: {application.yeepay_customer_code} -> {sub_cus_code}")

                            logger.info(f"准备更新数据库状态: {update_data}")
                            await AccountApplicationDao.update_application_status_by_notify(
                                query_db, application.uuid, update_data
                            )
                            logger.info(f"数据库状态更新完成")

                            return {
                                "has_application": True,
                                "need_apply": False,
                                "status": "2",
                                "status_name": "审核通过",
                                "application_uuid": application.uuid,
                                "yeepay_customer_code": sub_cus_code or application.yeepay_customer_code,
                                "message": "开户已完成（状态已更新）"
                            }

                        elif txn_state == "4":  # 易宝显示失败
                            logger.info(f"❌ 易宝显示开户失败")
                            # 更新申请状态为审核拒绝
                            update_data = {
                                "status": "3",
                                "remark": "易宝查询确认开户失败"
                            }
                            await AccountApplicationDao.update_application_status_by_notify(
                                query_db, application.uuid, update_data
                            )
                            return {
                                "has_application": True,
                                "need_apply": True,
                                "status": "3",
                                "status_name": "审核拒绝",
                                "application_uuid": application.uuid,
                                "message": "开户失败，请重新申请"
                            }

                        else:  # 其他状态（处理中等）
                            logger.info(f"⏳ 易宝显示处理中，状态: {txn_state}")
                            return {
                                "has_application": True,
                                "need_apply": False,
                                "status": "1",
                                "status_name": "审核中",
                                "application_uuid": application.uuid,
                                "message": "开户申请处理中，请稍后查询"
                            }

                    except Exception as e:
                        logger.error(f"查询易宝结果异常: {str(e)}")
                        import traceback
                        logger.error(f"异常堆栈: {traceback.format_exc()}")

                        # 查询失败，返回当前状态
                        status_map = {"0": "待审核", "1": "审核中", "2": "审核通过", "3": "审核拒绝"}
                        return {
                            "has_application": True,
                            "need_apply": False,
                            "status": application.status,
                            "status_name": status_map.get(application.status, "未知"),
                            "application_uuid": application.uuid,
                            "message": "状态查询中，请稍后再试"
                        }
                else:
                    # 没有易宝信息，可能是旧数据或异常情况
                    logger.warning(f"申请记录缺少易宝信息")
                    status_map = {"0": "待审核", "1": "审核中", "2": "审核通过", "3": "审核拒绝"}
                    return {
                        "has_application": True,
                        "need_apply": application.status == "3",  # 审核拒绝的可以重新申请
                        "status": application.status,
                        "status_name": status_map.get(application.status, "未知"),
                        "application_uuid": application.uuid,
                        "message": "申请状态异常，请联系客服"
                    }

            # 其他状态
            status_map = {"0": "待审核", "1": "审核中", "2": "审核通过", "3": "审核拒绝"}
            return {
                "has_application": True,
                "need_apply": application.status == "3",  # 审核拒绝的可以重新申请
                "status": application.status,
                "status_name": status_map.get(application.status, "未知"),
                "application_uuid": application.uuid,
                "message": "请等待处理结果"
            }

        except Exception as e:
            logger.error(f"检查开户状态异常: {str(e)}")
            raise BusinessException(message=f"检查开户状态失败: {str(e)}")


