from fastapi import APIRouter, Depends, Request, Query, Form, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any, List
from config.get_db import get_db
from module_admin.service.merge_service import MergeService
from module_admin.service.auth_adapter import AuthAdapter
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, ResourceNotFoundException, BusinessException, ValidationException

# 使用统一的API路由前缀格式
merge_controller = APIRouter(prefix='/api/v1/merge', dependencies=[Depends(AuthAdapter.get_current_user)])

@merge_controller.get('/getList', summary="获取合单列表")
async def get_merge_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    status: Optional[str] = Query(None, description="状态"),
    keyword: Optional[str] = Query(None, description="关键词"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取合单列表接口
    
    分页获取合单列表，支持按状态和关键词筛选
    
    原始API路径: /mergev3/getList
    """
    try:
        # 调用服务层获取合单列表
        result = await MergeService.get_merge_list_service(query_db, page, size, status, keyword)
        
        return ResponseUtil.success(
            msg="获取合单列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询合单列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取合单列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取合单列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@merge_controller.get('/getOne', summary="获取单个合单信息")
async def get_merge_one(
    request: Request,
    uuid: str = Query(..., description="合单UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取单个合单信息接口
    
    根据UUID获取合单的详细信息
    
    原始API路径: /mergev3/getOne
    """
    try:
        # 调用服务层获取单个合单信息
        result = await MergeService.get_merge_one_service(query_db, uuid)
        
        return ResponseUtil.success(
            msg="获取单个合单信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取单个合单信息数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"合单不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询单个合单信息失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取单个合单信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取单个合单信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@merge_controller.post('/save', summary="保存合单信息")
async def save_merge(
    request: Request,
    uuid: Optional[str] = Form(None, description="合单UUID"),
    title: str = Form(..., description="标题"),
    description: Optional[str] = Form("", description="描述"),
    status: Optional[str] = Form("0", description="状态"),
    order_uuids: List[str] = Form(..., description="订单UUID列表"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """保存合单信息接口
    
    保存合单信息，包括基本信息和订单信息
    
    原始API路径: /mergev3/save
    """
    try:
        # 获取当前用户信息
        current_user = await AuthAdapter.get_current_user(request)
        
        # 构建合单数据
        merge_data = {
            "uuid": uuid,
            "title": title,
            "description": description,
            "status": status,
            "order_uuids": order_uuids,
            "creator_uuid": current_user.get("user_uuid")
        }
        
        # 调用服务层保存合单信息
        result = await MergeService.save_merge_service(query_db, merge_data)
        
        return ResponseUtil.success(
            msg="保存合单信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"保存合单信息数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"保存合单信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"保存合单信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@merge_controller.post('/delete', summary="删除合单")
async def delete_merge(
    request: Request,
    uuid: str = Form(..., description="合单UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """删除合单接口
    
    根据UUID删除合单
    
    原始API路径: /mergev3/delete
    """
    try:
        # 调用服务层删除合单
        result = await MergeService.delete_merge_service(query_db, uuid)
        
        return ResponseUtil.success(
            msg="删除合单成功",
            data={"success": result}
        )
    except ValidationException as e:
        logger.error(f"删除合单数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"合单不存在或删除失败: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"删除合单业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"删除合单异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@merge_controller.post('/updateStatus', summary="更新合单状态")
async def update_merge_status(
    request: Request,
    uuid: str = Form(..., description="合单UUID"),
    status: str = Form(..., description="状态"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """更新合单状态接口
    
    根据UUID更新合单状态
    
    原始API路径: /mergev3/updateStatus
    """
    try:
        # 调用服务层更新合单状态
        result = await MergeService.update_merge_status_service(query_db, uuid, status)
        
        return ResponseUtil.success(
            msg="更新合单状态成功",
            data={"success": result}
        )
    except ValidationException as e:
        logger.error(f"更新合单状态数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"合单不存在或更新状态失败: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"更新合单状态业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"更新合单状态异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@merge_controller.get('/getOrderListForMerge', summary="获取可合单的订单列表")
async def get_order_list_for_merge(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    keyword: Optional[str] = Query(None, description="关键词"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取可合单的订单列表接口
    
    分页获取可合单的订单列表，支持按关键词筛选
    
    原始API路径: /mergev3/getOrderListForMerge
    """
    try:
        # 调用服务层获取可合单的订单列表
        result = await MergeService.get_order_list_for_merge_service(query_db, page, size, keyword)
        
        return ResponseUtil.success(
            msg="获取可合单的订单列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询可合单的订单列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取可合单的订单列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取可合单的订单列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
