<template>
  <view class="innet-container">
    <appHead title="易宝支付入网申请"></appHead>
    
    <view class="content">
      <!-- 入网类型选择 -->
      <view class="section">
        <view class="section-title">选择入网类型</view>
        <view class="type-selector">
          <view 
            class="type-item" 
            :class="{ active: innetType === 'enterprise' }"
            @click="selectInnetType('enterprise')"
          >
            <view class="type-icon">🏢</view>
            <view class="type-text">企业/个体户</view>
            <view class="type-desc">适用于有营业执照的企业和个体工商户</view>
          </view>
          
          <view 
            class="type-item" 
            :class="{ active: innetType === 'micro' }"
            @click="selectInnetType('micro')"
          >
            <view class="type-icon">👤</view>
            <view class="type-text">小微/个人</view>
            <view class="type-desc">适用于小微企业和个人用户</view>
          </view>
        </view>
      </view>
      
      <!-- 入网表单 -->
      <view class="section" v-if="innetType">
        <view class="section-title">填写入网信息</view>
        
        <!-- 主体信息 -->
        <view class="form-group">
          <view class="group-title">主体信息</view>
          
          <view class="form-item">
            <view class="label">
              {{ innetType === 'micro' ? '姓名' : '主体名称' }} <text class="required">*</text>
            </view>
            <input
              class="input"
              v-model="formData.ownerName"
              :placeholder="innetType === 'micro' ? '请输入姓名（将从身份证自动识别）' : '请输入主体名称'"
              :disabled="innetType === 'micro'"
            />
          </view>

          <view class="form-item">
            <view class="label">
              {{ innetType === 'micro' ? '商户简称' : '主体简称' }} <text class="required">*</text>
            </view>
            <input
              class="input"
              v-model="formData.ownerShortName"
              :placeholder="innetType === 'micro' ? '请输入商户简称（显示在收银台）' : '请输入主体简称'"
            />
          </view>
          
          <!-- 企业/个体户特有字段 -->
          <template v-if="innetType === 'enterprise'">
            <view class="form-item">
              <view class="label">主体类型 <text class="required">*</text></view>
              <picker 
                :value="ownerTypeIndex" 
                :range="ownerTypeOptions" 
                range-key="text"
                @change="onOwnerTypeChange"
              >
                <view class="picker">
                  {{ (ownerTypeOptions[ownerTypeIndex] && ownerTypeOptions[ownerTypeIndex].text) || '请选择主体类型' }}
                </view>
              </picker>
            </view>
            
            <view class="form-item">
              <view class="label">统一社会信用代码 <text class="required">*</text></view>
              <input 
                class="input" 
                v-model="formData.ownerCertNo" 
                placeholder="请输入统一社会信用代码"
              />
            </view>
            
            <view class="form-item">
              <view class="label">营业执照照片 <text class="required">*</text></view>
              <view class="upload-area" @click="uploadOwnerCert">
                <image 
                  v-if="formData.ownerCertPic" 
                  :src="formData.ownerCertPicUrl" 
                  class="upload-image"
                />
                <view v-else class="upload-placeholder">
                  <text class="upload-icon">📷</text>
                  <text class="upload-text">点击上传营业执照</text>
                </view>
              </view>
            </view>
          </template>
        </view>
        
        <!-- 法人信息 -->
        <view class="form-group">
          <view class="group-title">{{ innetType === 'micro' ? '个人信息' : '法人信息' }}</view>
          
          <view class="form-item">
            <view class="label">
              {{ innetType === 'micro' ? '姓名' : '法人姓名' }} <text class="required">*</text>
            </view>
            <input
              class="input"
              v-model="formData.juridicalName"
              :placeholder="innetType === 'micro' ? '请输入姓名（将从身份证自动识别）' : '请输入法人姓名'"
              :disabled="innetType === 'micro'"
            />
          </view>

          <view class="form-item">
            <view class="label">
              {{ innetType === 'micro' ? '身份证号' : '法人身份证号' }} <text class="required">*</text>
            </view>
            <input
              class="input"
              v-model="formData.juridicalCertNo"
              :placeholder="innetType === 'micro' ? '请输入身份证号（将从身份证自动识别）' : '请输入法人身份证号'"
              :disabled="innetType === 'micro'"
            />
          </view>

          <view class="form-item">
            <view class="label">
              {{ innetType === 'micro' ? '手机号' : '法人手机号' }}
            </view>
            <input
              class="input"
              v-model="formData.juridicalMobile"
              :placeholder="innetType === 'micro' ? '请输入手机号' : '请输入法人手机号'"
            />
          </view>
          
          <view class="form-item">
            <view class="label">身份证人像面 <text class="required">*</text></view>
            <view class="form-tips" v-if="innetType === 'micro'">
              <text>💡 上传后将自动识别姓名和身份证号</text>
            </view>
            <view class="upload-area" @click="uploadJuridicalCert">
              <image
                v-if="formData.juridicalCertPic"
                :src="formData.juridicalCertPicUrl"
                class="upload-image"
              />
              <view v-else class="upload-placeholder">
                <text class="upload-icon">📷</text>
                <text class="upload-text">点击上传身份证人像面</text>
              </view>
            </view>
          </view>

          <view class="form-item">
            <view class="label">身份证非人像面 <text class="required">*</text></view>
            <view class="upload-area" @click="uploadJuridicalCertBack">
              <image
                v-if="formData.juridicalCertBackPic"
                :src="formData.juridicalCertBackPicUrl"
                class="upload-image"
              />
              <view v-else class="upload-placeholder">
                <text class="upload-icon">📷</text>
                <text class="upload-text">点击上传身份证非人像面</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 联系人信息（仅企业/个体户需要） -->
        <view class="form-group" v-if="innetType === 'enterprise'">
          <view class="group-title">联系人信息</view>

          <view class="form-item">
            <view class="label">联系人姓名 <text class="required">*</text></view>
            <input
              class="input"
              v-model="formData.contactName"
              placeholder="请输入联系人姓名"
            />
          </view>

          <view class="form-item">
            <view class="label">联系人手机号 <text class="required">*</text></view>
            <input
              class="input"
              v-model="formData.contactMobile"
              placeholder="请输入联系人手机号"
            />
          </view>

          <view class="form-item">
            <view class="label">联系人邮箱</view>
            <input
              class="input"
              v-model="formData.contactEmail"
              placeholder="请输入联系人邮箱"
            />
          </view>
        </view>

        <!-- 经营地址信息 -->
        <view class="form-group">
          <view class="group-title">经营地址信息</view>

          <view class="form-item">
            <view class="label">省份 <text class="required">*</text></view>
            <input
              class="input"
              v-model="formData.province"
              placeholder="请输入省份"
            />
          </view>

          <view class="form-item">
            <view class="label">城市 <text class="required">*</text></view>
            <input
              class="input"
              v-model="formData.city"
              placeholder="请输入城市"
            />
          </view>

          <view class="form-item">
            <view class="label">区县 <text class="required">*</text></view>
            <input
              class="input"
              v-model="formData.district"
              placeholder="请输入区县"
            />
          </view>

          <view class="form-item">
            <view class="label">详细地址 <text class="required">*</text></view>
            <input
              class="input"
              v-model="formData.address"
              placeholder="请输入详细地址"
            />
          </view>
        </view>

        <!-- 结算账户信息 -->
        <view class="form-group">
          <view class="group-title">结算账户信息</view>
          <view class="form-tips">
            <text>💡 结算账户用于接收交易资金，请确保信息准确无误</text>
          </view>

          <view class="form-item">
            <view class="label">账户类型 <text class="required">*</text></view>
            <picker
              :value="accountTypeIndex"
              :range="accountTypeOptions"
              range-key="text"
              @change="onAccountTypeChange"
            >
              <view class="picker">
                {{ (accountTypeOptions[accountTypeIndex] && accountTypeOptions[accountTypeIndex].text) || '请选择账户类型' }}
              </view>
            </picker>
          </view>

          <view class="form-item">
            <view class="label">银行账号 <text class="required">*</text></view>
            <input
              class="input"
              v-model="formData.accountNo"
              placeholder="请输入银行账号"
            />
          </view>

          <view class="form-item">
            <view class="label">账户名称 <text class="required">*</text></view>
            <input
              class="input"
              v-model="formData.accountName"
              placeholder="请输入账户名称"
            />
          </view>

          <view class="form-item">
            <view class="label">开户行 <text class="required">*</text></view>
            <picker
              :value="bankIndex"
              :range="bankOptions"
              range-key="name"
              @change="onBankChange"
            >
              <view class="picker">
                {{ (bankOptions[bankIndex] && bankOptions[bankIndex].name) || '请选择开户行' }}
              </view>
            </picker>
          </view>

          <view class="form-item" v-if="formData.bankCode">
            <view class="label">开户行代码</view>
            <input
              class="input"
              v-model="formData.bankCode"
              placeholder="开户行代码"
              disabled
            />
          </view>

          <view class="form-item">
            <view class="label">开户行名称</view>
            <input
              class="input"
              v-model="formData.bankName"
              placeholder="请输入开户行名称"
            />
          </view>

          <view class="form-item">
            <view class="label">结算周期</view>
            <picker
              :value="settleCycleIndex"
              :range="settleCycleOptions"
              range-key="text"
              @change="onSettleCycleChange"
            >
              <view class="picker">
                {{ (settleCycleOptions[settleCycleIndex] && settleCycleOptions[settleCycleIndex].text) || 'T+1' }}
              </view>
            </picker>
          </view>
        </view>
      </view>
      
      <!-- 提交按钮 -->
      <view class="submit-section" v-if="innetType">
        <button 
          class="submit-btn" 
          :class="{ disabled: submitting }"
          @click="submitInnet"
          :disabled="submitting"
        >
          {{ submitting ? '提交中...' : '提交入网申请' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import {
  enterpriseInnet,
  microInnet,
  uploadInnetFile,
  buildEnterpriseInnetData,
  buildMicroInnetData,
  INNET_CONSTANTS
} from '@/api/yeepay-innet.js'
import { recognizeBusinessLicenseFromTempFile } from '@/api/account-application.js'

export default {
  data() {
    return {
      innetType: '', // 'enterprise' 或 'micro'
      submitting: false,
      fromBalanceManage: false, // 是否从余额管理页面跳转过来

      // 身份证识别相关
      idCardRecognition: {
        isRecognizing: false,
        frontBase64: '',
        backBase64: ''
      },
      
      // 主体类型选项
      ownerTypeIndex: 0,
      ownerTypeOptions: [
        { value: INNET_CONSTANTS.OWNER_TYPES.ENTERPRISE, text: '企业' },
        { value: INNET_CONSTANTS.OWNER_TYPES.INDIVIDUAL, text: '个体工商户' }
      ],

      // 账户类型选项
      accountTypeIndex: 0,
      accountTypeOptions: [
        { value: INNET_CONSTANTS.ACCOUNT_TYPES.PERSONAL_DEBIT, text: '个人借记卡' },
        { value: INNET_CONSTANTS.ACCOUNT_TYPES.CORPORATE_GENERAL, text: '对公一般户' },
        { value: INNET_CONSTANTS.ACCOUNT_TYPES.ENTERPRISE_SETTLEMENT, text: '企业单位结算卡' }
      ],

      // 结算周期选项
      settleCycleIndex: 1, // 默认选择T+1
      settleCycleOptions: [
        { value: INNET_CONSTANTS.SETTLE_CYCLES.T0, text: 'T+0（当日到账）' },
        { value: INNET_CONSTANTS.SETTLE_CYCLES.T1, text: 'T+1（次日到账）' }
      ],

      // 常用银行选项
      bankIndex: -1,
      bankOptions: [
        { code: '************', name: '工商银行' },
        { code: '************', name: '农业银行' },
        { code: '************', name: '中国银行' },
        { code: '************', name: '建设银行' },
        { code: '************', name: '交通银行' },
        { code: '************', name: '中信银行' },
        { code: '************', name: '光大银行' },
        { code: '************', name: '民生银行' },
        { code: '************', name: '平安银行' },
        { code: '************', name: '招商银行' },
        { code: '************', name: '兴业银行' },
        { code: '************', name: '浦发银行' }
      ],
      
      // 表单数据
      formData: {
        // 主体信息
        ownerType: INNET_CONSTANTS.OWNER_TYPES.ENTERPRISE,
        ownerName: '',
        ownerShortName: '',
        ownerCertNo: '',
        ownerCertPic: '',
        ownerCertPicUrl: '',
        
        // 法人信息
        juridicalName: '',
        juridicalCertNo: '',
        juridicalMobile: '',
        juridicalCertPic: '',
        juridicalCertPicUrl: '',
        juridicalCertBackPic: '',
        juridicalCertBackPicUrl: '',
        
        // 联系人信息
        contactName: '',
        contactMobile: '',
        contactEmail: '',

        // 经营地址信息
        province: '',
        city: '',
        district: '',
        address: '',

        // 结算账户信息
        accountType: INNET_CONSTANTS.ACCOUNT_TYPES.PERSONAL_DEBIT,
        accountNo: '',
        accountName: '',
        bankCode: '',
        bankName: '',
        settleCycle: INNET_CONSTANTS.SETTLE_CYCLES.T1,

        // 经营地址信息
        province: '',
        city: '',
        district: '',
        address: '',

        // 结算账户信息
        accountType: INNET_CONSTANTS.ACCOUNT_TYPES.PERSONAL_DEBIT,
        accountNo: '',
        accountName: '',
        bankCode: '',
        bankName: '',
        settleCycle: INNET_CONSTANTS.SETTLE_CYCLES.T1
      }
    }
  },
  
  methods: {
    // 选择入网类型
    selectInnetType(type) {
      this.innetType = type
      this.resetFormData()
    },
    
    // 重置表单数据
    resetFormData() {
      this.formData = {
        ownerType: INNET_CONSTANTS.OWNER_TYPES.ENTERPRISE,
        ownerName: '',
        ownerShortName: '',
        ownerCertNo: '',
        ownerCertPic: '',
        ownerCertPicUrl: '',
        juridicalName: '',
        juridicalCertNo: '',
        juridicalMobile: '',
        juridicalCertPic: '',
        juridicalCertPicUrl: '',
        juridicalCertBackPic: '',
        juridicalCertBackPicUrl: '',
        contactName: '',
        contactMobile: '',
        contactEmail: '',
        province: '',
        city: '',
        district: '',
        address: '',
        accountType: INNET_CONSTANTS.ACCOUNT_TYPES.PERSONAL_DEBIT,
        accountNo: '',
        accountName: '',
        bankCode: '',
        bankName: '',
        settleCycle: INNET_CONSTANTS.SETTLE_CYCLES.T1
      }
    },
    
    // 主体类型变化
    onOwnerTypeChange(e) {
      this.ownerTypeIndex = e.detail.value
      this.formData.ownerType = this.ownerTypeOptions[this.ownerTypeIndex].value
    },

    // 账户类型变化
    onAccountTypeChange(e) {
      this.accountTypeIndex = e.detail.value
      this.formData.accountType = this.accountTypeOptions[this.accountTypeIndex].value
    },

    // 结算周期变化
    onSettleCycleChange(e) {
      this.settleCycleIndex = e.detail.value
      this.formData.settleCycle = this.settleCycleOptions[this.settleCycleIndex].value
    },

    // 银行选择变化
    onBankChange(e) {
      this.bankIndex = e.detail.value
      const selectedBank = this.bankOptions[this.bankIndex]
      if (selectedBank) {
        this.formData.bankCode = selectedBank.code
        this.formData.bankName = selectedBank.name
      }
    },

    // 带OCR识别的身份证上传
    uploadIdCardWithOCR(type) {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['camera', 'album'],
        success: async (res) => {
          try {
            const tempFilePath = res.tempFilePaths[0]

            // 根据类型设置对应的字段
            if (type === 'juridicalCert') {
              this.formData.juridicalCertPicUrl = tempFilePath
            } else if (type === 'juridicalCertBack') {
              this.formData.juridicalCertBackPicUrl = tempFilePath
            }

            // 如果是正面身份证，进行OCR识别
            if (type === 'juridicalCert') {
              await this.recognizeIdCard(tempFilePath)
            } else {
              // 反面直接上传文件
              await this.uploadFileToServer(tempFilePath, type)
            }

          } catch (error) {
            uni.showToast({ title: '上传失败', icon: 'error' })
            console.error('身份证上传失败:', error)
          }
        },
        fail: (error) => {
          uni.showToast({ title: '选择图片失败', icon: 'error' })
          console.error('选择图片失败:', error)
        }
      })
    },

    // 身份证识别主方法
    async recognizeIdCard(filePath) {
      if (this.idCardRecognition.isRecognizing) {
        uni.showToast({
          title: '正在识别中，请稍候',
          icon: 'none'
        })
        return
      }

      this.idCardRecognition.isRecognizing = true

      try {
        uni.showLoading({
          title: '图片上传中...'
        })

        // 1. 先上传图片到服务器
        const uploadResult = await this.uploadFileToServer(filePath, 'juridicalCert')

        if (!uploadResult.success) {
          throw new Error(uploadResult.message || '图片上传失败')
        }

        uni.showLoading({
          title: '身份证识别中...'
        })

        // 2. 转换为base64用于OCR识别
        const base64Data = this.urlToBase64(filePath)
        if (!base64Data) {
          throw new Error('图片处理失败')
        }

        // 3. 调用OCR识别（只识别正面）
        this.idCardRecognition.frontBase64 = base64Data
        await this.recognizeIdCardFront(base64Data)

      } catch (error) {
        console.error('身份证识别失败:', error)
        uni.showToast({
          title: error.message || '识别失败，请重试',
          icon: 'none',
          duration: 3000
        })
      } finally {
        uni.hideLoading()
        this.idCardRecognition.isRecognizing = false
      }
    },
    
    // 上传营业执照
    uploadOwnerCert() {
      this.uploadFileWithOCR('ownerCert')
    },
    
    // 上传法人身份证人像面
    uploadJuridicalCert() {
      this.uploadIdCardWithOCR('juridicalCert')
    },

    // 上传法人身份证非人像面
    uploadJuridicalCertBack() {
      this.uploadIdCardWithOCR('juridicalCertBack')
    },
    
    // 通用文件上传方法
    uploadFile(type) {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['camera', 'album'],
        success: async (res) => {
          try {
            uni.showLoading({ title: '上传中...' })
            
            const tempFilePath = res.tempFilePaths[0]
            
            // 这里应该调用文件上传API
            // 由于uni.chooseImage返回的是本地路径，需要转换为File对象
            // 在实际项目中，可能需要使用uni.uploadFile或其他方式
            
            // 模拟上传成功
            const fileId = 'mock_file_id_' + Date.now()
            
            // 根据类型设置对应的字段
            if (type === 'ownerCert') {
              this.formData.ownerCertPic = fileId
              this.formData.ownerCertPicUrl = tempFilePath
            } else if (type === 'juridicalCert') {
              this.formData.juridicalCertPic = fileId
              this.formData.juridicalCertPicUrl = tempFilePath
            } else if (type === 'juridicalCertBack') {
              this.formData.juridicalCertBackPic = fileId
              this.formData.juridicalCertBackPicUrl = tempFilePath
            }
            
            uni.hideLoading()
            uni.showToast({ title: '上传成功', icon: 'success' })
            
          } catch (error) {
            uni.hideLoading()
            uni.showToast({ title: '上传失败', icon: 'error' })
            console.error('文件上传失败:', error)
          }
        },
        fail: (error) => {
          uni.showToast({ title: '选择图片失败', icon: 'error' })
          console.error('选择图片失败:', error)
        }
      })
    },

    // 带OCR识别的文件上传方法（用于营业执照）
    uploadFileWithOCR(type) {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['camera', 'album'],
        success: async (res) => {
          try {
            uni.showLoading({ title: '上传中...' })

            const tempFilePath = res.tempFilePaths[0]

            // 同时进行文件上传和OCR识别
            const [uploadResult, ocrResult] = await Promise.allSettled([
              this.performFileUpload(tempFilePath, type),
              this.recognizeBusinessLicenseFromTempFile(tempFilePath)
            ])

            // 处理上传结果
            if (uploadResult.status === 'fulfilled') {
              // 根据类型设置对应的字段
              if (type === 'ownerCert') {
                this.formData.ownerCertPic = uploadResult.value.fileId
                this.formData.ownerCertPicUrl = uploadResult.value.url || tempFilePath
              }
              uni.showToast({ title: '上传成功', icon: 'success' })
            } else {
              console.error('文件上传失败:', uploadResult.reason)
              uni.showToast({ title: '文件上传失败', icon: 'none' })
            }

            // 处理OCR识别结果
            if (ocrResult.status === 'fulfilled' && type === 'ownerCert') {
              const data = ocrResult.value.data
              if (data) {
                // 自动填充企业信息
                this.formData.enterpriseName = data.company_name || ''
                this.formData.creditCode = data.register_number || ''
                this.formData.contactName = data.legal_person || ''
                this.formData.businessAddress = data.address || ''

                uni.showToast({ title: '营业执照信息识别成功', icon: 'success' })
              }
            } else if (ocrResult.status === 'rejected') {
              console.error('OCR识别失败:', ocrResult.reason)
              uni.showToast({ title: 'OCR识别失败，请手动填写', icon: 'none' })
            }

            uni.hideLoading()

          } catch (error) {
            uni.hideLoading()
            uni.showToast({ title: '操作失败', icon: 'error' })
            console.error('文件上传失败:', error)
          }
        }
      })
    },

    // 执行文件上传
    async performFileUpload(tempFilePath, type) {
      // 这里应该调用真实的文件上传API
      // 目前返回模拟数据
      return new Promise((resolve) => {
        setTimeout(() => {
          const fileId = 'mock_file_id_' + Date.now()
          resolve({ fileId, url: tempFilePath })
        }, 1000)
      })
    },

    // 从临时文件路径创建营业执照OCR识别的文件对象
    async recognizeBusinessLicenseFromTempFile(tempFilePath) {
      return recognizeBusinessLicenseFromTempFile(tempFilePath)
    },
    
    // 提交入网申请
    async submitInnet() {
      try {
        // 验证表单
        if (!this.validateForm()) {
          return
        }
        
        this.submitting = true
        uni.showLoading({ title: '提交中...' })
        
        let result
        
        if (this.innetType === 'enterprise') {
          // 企业/个体户入网
          const requestData = buildEnterpriseInnetData(this.formData)
          result = await enterpriseInnet(requestData)
        } else {
          // 小微/个人入网
          const requestData = buildMicroInnetData(this.formData)
          result = await microInnet(requestData)
        }
        
        uni.hideLoading()
        
        if (result.code === 200) {
          uni.showToast({ title: '提交成功', icon: 'success' })
          
          // 跳转到结果页面或返回上一页
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          uni.showToast({ title: result.msg || '提交失败', icon: 'error' })
        }
        
      } catch (error) {
        uni.hideLoading()
        uni.showToast({ title: '提交失败', icon: 'error' })
        console.error('提交入网申请失败:', error)
      } finally {
        this.submitting = false
      }
    },
    
    // 验证表单
    validateForm() {
      const { formData, innetType } = this
      
      // 基础验证
      if (!formData.ownerName) {
        uni.showToast({ title: '请输入主体名称', icon: 'error' })
        return false
      }
      
      if (!formData.ownerShortName) {
        uni.showToast({ title: '请输入主体简称', icon: 'error' })
        return false
      }
      
      if (!formData.juridicalName) {
        uni.showToast({ title: '请输入法人姓名', icon: 'error' })
        return false
      }
      
      if (!formData.juridicalCertNo) {
        uni.showToast({ title: '请输入法人身份证号', icon: 'error' })
        return false
      }
      
      if (!formData.juridicalCertPic) {
        uni.showToast({ title: '请上传身份证人像面', icon: 'error' })
        return false
      }
      
      if (!formData.juridicalCertBackPic) {
        uni.showToast({ title: '请上传身份证非人像面', icon: 'error' })
        return false
      }
      
      // 企业/个体户特有验证
      if (innetType === 'enterprise') {
        if (!formData.ownerCertNo) {
          uni.showToast({ title: '请输入统一社会信用代码', icon: 'error' })
          return false
        }

        if (!formData.ownerCertPic) {
          uni.showToast({ title: '请上传营业执照', icon: 'error' })
          return false
        }

        if (!formData.contactName) {
          uni.showToast({ title: '请输入联系人姓名', icon: 'error' })
          return false
        }

        if (!formData.contactMobile) {
          uni.showToast({ title: '请输入联系人手机号', icon: 'error' })
          return false
        }
      }

      // 经营地址验证
      if (!formData.province) {
        uni.showToast({ title: '请输入省份', icon: 'error' })
        return false
      }

      if (!formData.city) {
        uni.showToast({ title: '请输入城市', icon: 'error' })
        return false
      }

      if (!formData.district) {
        uni.showToast({ title: '请输入区县', icon: 'error' })
        return false
      }

      if (!formData.address) {
        uni.showToast({ title: '请输入详细地址', icon: 'error' })
        return false
      }

      // 结算账户验证
      if (!formData.accountNo) {
        uni.showToast({ title: '请输入银行账号', icon: 'error' })
        return false
      }

      if (!formData.accountName) {
        uni.showToast({ title: '请输入账户名称', icon: 'error' })
        return false
      }

      if (!formData.bankCode) {
        uni.showToast({ title: '请输入开户行代码', icon: 'error' })
        return false
      }

      return true
    },

    // 身份证正面识别
    recognizeIdCardFront(base64Data) {
      return new Promise((resolve, reject) => {
        const requestData = {
          file: base64Data,
          cardTpe: 0  // 0表示正面
        }

        uni.request({
          url: 'https://agentapi.xiaoyujia.com/files/imgsToIdInfo',
          method: 'POST',
          header: {
            'content-type': 'application/json;charset=UTF-8'
          },
          data: JSON.stringify(requestData),
          success: (res) => {
            try {
              const status = res.data.image_status
              if (status === 'normal') {
                // 识别成功
                const wordsResult = res.data.words_result

                // 自动填充识别到的信息
                if (wordsResult.姓名) {
                  this.formData.juridicalName = wordsResult.姓名.words
                  // 对于个人入网，同时填充主体名称
                  if (this.innetType === 'micro') {
                    this.formData.ownerName = wordsResult.姓名.words
                    this.formData.ownerShortName = wordsResult.姓名.words
                  }
                }

                if (wordsResult.公民身份号码) {
                  this.formData.juridicalCertNo = wordsResult.公民身份号码.words
                }

                uni.showToast({
                  title: '身份证识别成功',
                  icon: 'success',
                  duration: 2000
                })

                resolve(wordsResult)
              } else {
                reject(new Error('身份证识别失败，请重新拍照'))
              }
            } catch (error) {
              console.error('解析识别结果失败:', error)
              reject(new Error('识别结果解析失败'))
            }
          },
          fail: (error) => {
            console.error('OCR识别请求失败:', error)
            reject(new Error('识别服务请求失败'))
          }
        })
      })
    },

    // 图片转base64工具方法
    urlToBase64(url) {
      try {
        const imgData = uni.getFileSystemManager().readFileSync(url, 'base64')
        return 'data:image/jpeg;base64,' + imgData
      } catch (error) {
        console.error('图片转base64失败:', error)
        return ''
      }
    },

    // 上传文件到服务器
    async uploadFileToServer(filePath, type) {
      return new Promise((resolve, reject) => {
        // 这里应该调用实际的文件上传API
        // 模拟上传成功
        setTimeout(() => {
          const fileId = 'mock_file_id_' + Date.now()

          // 根据类型设置对应的字段
          if (type === 'ownerCert') {
            this.formData.ownerCertPic = fileId
          } else if (type === 'juridicalCert') {
            this.formData.juridicalCertPic = fileId
          } else if (type === 'juridicalCertBack') {
            this.formData.juridicalCertBackPic = fileId
          }

          resolve({ success: true, fileId })
        }, 1000)
      })
    }
  },

  // 页面生命周期
  onLoad(options) {
    // 检查是否有传入的类型参数
    if (options.type) {
      this.innetType = options.type
      this.fromBalanceManage = true

      // 如果是从余额管理页面跳转过来，自动选择对应类型
      if (options.type === 'enterprise' || options.type === 'micro') {
        this.resetFormData()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.innet-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 20rpx;
}

.section {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-title {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
}

/* 入网类型选择 */
.type-selector {
  padding: 30rpx;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 30rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &.active {
    border-color: #007aff;
    background-color: #f0f8ff;
  }
}

.type-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.type-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.type-desc {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  line-height: 1.4;
}

/* 表单样式 */
.form-group {
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.group-title {
  padding: 30rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  background-color: #fafafa;
}

.form-tips {
  padding: 20rpx 30rpx;
  background-color: #f0f8ff;
  border-left: 4rpx solid #007aff;
  margin-bottom: 20rpx;

  text {
    font-size: 24rpx;
    color: #666;
    line-height: 1.5;
  }
}

.form-item {
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;

  .required {
    color: #ff4757;
  }
}

.input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.picker {
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 20rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
  color: #333;
}

/* 文件上传样式 */
.upload-area {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ccc;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  transition: all 0.3s ease;

  &:active {
    background-color: #f0f0f0;
  }
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 40rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

/* 提交按钮 */
.submit-section {
  padding: 40rpx 20rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;

  &.disabled {
    background: #ccc;
    color: #999;
  }

  &:not(.disabled):active {
    background: linear-gradient(135deg, #0056cc 0%, #003d99 100%);
  }
}
</style>
