from fastapi import APIRouter, Depends, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
import json

from module_admin.service.account_application_service import AccountApplicationService
from module_admin.entity.vo.account_application_vo import (
    AccountApplicationSubmitVO,
    AccountApplicationReviewVO
)
from module_admin.service.service_staff_login_service import ServiceStaffLoginService
from config.get_db import get_db
from utils.response_util import ResponseUtil
from exceptions.exception import ValidationException, BusinessException
from utils.log_util import logger

# 创建路由器
staff_account_application_controller = APIRouter(prefix="/api/v1/staff/account/application", tags=["员工开户申请"])


@staff_account_application_controller.post('/submit', summary="员工提交开户申请")
async def submit_application(
    application_data: AccountApplicationSubmitVO,
    current_staff=Depends(ServiceStaffLoginService.get_current_staff),
    query_db: AsyncSession = Depends(get_db)
):
    """员工提交开户申请接口
    
    提交个人开户申请信息
    
    Args:
        application_data: 申请数据
        current_staff: 当前登录员工信息
        query_db: 数据库会话
        
    Returns:
        申请结果
    """
    try:
        # 获取当前员工手机号
        mobile = getattr(current_staff, 'mobile', None)
        
        if not mobile:
            logger.warning("当前员工手机号获取失败")
            raise ValidationException(message="当前员工手机号获取失败")
            
        logger.info(f"员工 {mobile} 提交开户申请")
        
        # 调用服务层提交申请
        result = await AccountApplicationService.submit_application(
            query_db, application_data, mobile
        )
        
        return ResponseUtil.success(
            msg="开户申请提交成功",
            data=result
        )
        
    except ValidationException as e:
        logger.warning(f"员工提交开户申请参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"员工提交开户申请失败: {str(e)}")
        return ResponseUtil.error(msg="提交开户申请失败")


@staff_account_application_controller.get('/status', summary="员工查询开户申请状态")
async def get_application_status(
    current_staff=Depends(ServiceStaffLoginService.get_current_staff),
    query_db: AsyncSession = Depends(get_db)
):
    """员工查询开户申请状态接口
    
    查询当前员工的开户申请状态
    
    Args:
        current_staff: 当前登录员工信息
        query_db: 数据库会话
        
    Returns:
        申请状态信息
    """
    try:
        # 获取当前员工手机号
        mobile = getattr(current_staff, 'mobile', None)
        
        if not mobile:
            logger.warning("当前员工手机号获取失败")
            raise ValidationException(message="当前员工手机号获取失败")
            
        logger.info(f"员工 {mobile} 查询开户申请状态")
        
        # 调用服务层查询状态
        result = await AccountApplicationService.get_application_status(query_db, mobile)
        
        return ResponseUtil.success(
            msg="查询申请状态成功",
            data=result
        )
        
    except ValidationException as e:
        logger.warning(f"员工查询申请状态参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"员工查询申请状态失败: {str(e)}")
        return ResponseUtil.error(msg="查询申请状态失败")


@staff_account_application_controller.get('/detail', summary="员工获取开户申请详情")
async def get_application_detail(
    uuid: str = Query(..., description="申请UUID"),
    current_staff=Depends(ServiceStaffLoginService.get_current_staff),
    query_db: AsyncSession = Depends(get_db)
):
    """员工获取开户申请详情接口
    
    获取指定申请的详细信息
    
    Args:
        uuid: 申请UUID
        current_staff: 当前登录员工信息
        query_db: 数据库会话
        
    Returns:
        申请详情信息
    """
    try:
        logger.info(f"员工获取开户申请详情，UUID: {uuid}")
        
        # 调用服务层获取详情
        result = await AccountApplicationService.get_application_detail(query_db, uuid)
        
        if not result:
            return ResponseUtil.error(msg="申请记录不存在")
        
        return ResponseUtil.success(
            msg="获取申请详情成功",
            data=result
        )
        
    except Exception as e:
        logger.error(f"员工获取申请详情失败: {str(e)}")
        return ResponseUtil.error(msg="获取申请详情失败")


@staff_account_application_controller.get('/status/check', summary="员工检查开户状态")
async def check_account_status(
    current_staff=Depends(ServiceStaffLoginService.get_current_staff),
    query_db: AsyncSession = Depends(get_db)
):
    """员工根据手机号检查开户状态接口

    用于员工端余额页面检查开户状态，自动查询易宝结果并更新状态

    Args:
        current_staff: 当前登录员工信息
        query_db: 数据库会话

    Returns:
        开户状态信息
    """
    try:
        # 获取当前员工手机号
        mobile = getattr(current_staff, 'mobile', None)
        
        if not mobile:
            logger.warning("当前员工手机号获取失败")
            raise ValidationException(message="当前员工手机号获取失败")
            
        logger.info(f"=== 员工检查开户状态请求 ===")
        logger.info(f"员工手机号: {mobile}")

        # 调用检查服务
        result = await AccountApplicationService.check_account_status_by_mobile_service(
            query_db,
            mobile
        )

        logger.info(f"员工检查开户状态完成: {result}")

        return ResponseUtil.success(
            data=result,
            msg="查询成功"
        )

    except ValidationException as e:
        logger.warning(f"员工检查开户状态参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"员工检查开户状态失败: {str(e)}")
        return ResponseUtil.error(msg="检查开户状态失败")
