"""
销售相关控制器
"""
from fastapi import APIRouter, Depends, Request, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional

from config.get_db import get_db
from config.enums import BusinessType
from module_admin.annotation.log_annotation import Log
from module_admin.service.internal_user_login_service import InternalUserLoginService


from utils.response_util import ResponseUtil
from utils.log_util import logger

# 创建销售相关的路由
salesController = APIRouter(prefix='/api/v1/sales')


@salesController.get('/attribution-staff-list')
@Log(title='获取销售归属人员列表', business_type=BusinessType.OTHER)
async def get_attribution_staff_list(
    request: Request,
    keyword: Optional[str] = Query(None, description="搜索关键词（姓名或手机号）"),
    current_user = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取销售归属人员列表
    合并服务人员和内部员工，用于销售归属选择
    """
    try:
        # 处理无效的搜索关键词
        if keyword in ['null', 'undefined', '', None]:
            keyword = None

        logger.info(f"获取销售归属人员列表，搜索关键词: {keyword}")

        # 获取当前用户的门店信息
        store_id = current_user.user.store_id
        store_uuid = current_user.user.store_uuid
        if not store_id:
            return ResponseUtil.error(msg="当前用户未关联门店")
        
        attribution_staff_list = []
        
        # 1. 获取服务人员列表
        try:
            # 直接使用SQL查询服务人员
            service_staff_sql = """
                SELECT id, uuid, real_name, mobile, avatar, status
                FROM service_staff
                WHERE store_uuid = :store_uuid
                  AND is_delete = '0'
                  AND status = '1'
            """

            params = {"store_uuid": store_uuid}

            # 如果有搜索关键词，添加搜索条件
            if keyword:
                if keyword.isdigit():
                    service_staff_sql += " AND mobile LIKE :keyword"
                else:
                    service_staff_sql += " AND real_name LIKE :keyword"
                params["keyword"] = f"%{keyword}%"

            service_staff_sql += " ORDER BY real_name ASC LIMIT 100"

            from sqlalchemy import text
            result = await query_db.execute(text(service_staff_sql), params)
            service_staff_rows = result.fetchall()

            for row in service_staff_rows:
                # 确保uuid字段存在且有效
                staff_uuid = row.uuid if row.uuid else str(row.id)
                attribution_staff_list.append({
                    'id': staff_uuid,
                    'uuid': staff_uuid,
                    'name': row.real_name or '',
                    'real_name': row.real_name or '',
                    'mobile': row.mobile or '',
                    'avatar': row.avatar or '',
                    'title': '',
                    'role_name': '服务人员',
                    'staff_type': 'service',
                    'status': row.status or '1'
                })

        except Exception as e:
            logger.warning(f"获取服务人员列表失败: {str(e)}")
        
        # 2. 获取内部员工列表
        try:
            # 直接使用SQL查询内部员工
            internal_user_sql = """
                SELECT id, name, mobile, avatar, title, role_name, status
                FROM internal_user
                WHERE store_id = :store_id
                  AND status = '1'
            """

            params = {"store_id": store_id}

            # 如果有搜索关键词，添加搜索条件
            if keyword:
                if keyword.isdigit():
                    internal_user_sql += " AND mobile LIKE :keyword"
                else:
                    internal_user_sql += " AND name LIKE :keyword"
                params["keyword"] = f"%{keyword}%"

            internal_user_sql += " ORDER BY name ASC LIMIT 100"

            result = await query_db.execute(text(internal_user_sql), params)
            internal_user_rows = result.fetchall()

            for row in internal_user_rows:
                attribution_staff_list.append({
                    'id': str(row.id),
                    'uuid': str(row.id),
                    'name': row.name or '',
                    'real_name': row.name or '',
                    'mobile': row.mobile or '',
                    'avatar': row.avatar or '',
                    'title': row.title or '',
                    'role_name': row.role_name or '内部员工',
                    'staff_type': 'internal',
                    'status': str(row.status or '1')
                })

        except Exception as e:
            logger.warning(f"获取内部员工列表失败: {str(e)}")
        
        # 3. 按姓名排序
        attribution_staff_list.sort(key=lambda x: x.get('name', ''))
        
        result = {
            'list': attribution_staff_list,
            'total': len(attribution_staff_list)
        }
        
        logger.info(f"获取销售归属人员列表成功，共 {len(attribution_staff_list)} 人")
        return ResponseUtil.success(data=result)
        
    except Exception as e:
        logger.error(f"获取销售归属人员列表失败: {str(e)}")
        return ResponseUtil.error(msg=f"获取销售归属人员列表失败: {str(e)}")
