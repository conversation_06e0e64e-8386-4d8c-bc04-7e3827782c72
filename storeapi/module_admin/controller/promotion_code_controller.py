from fastapi import APIRouter, Depends, Request, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any, List

from config.get_db import get_db
from module_admin.service.promotion_code_service import PromotionCodeService
from module_admin.service.auth_adapter import AuthAdapter
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, ResourceNotFoundException, BusinessException, ValidationException

# 创建两个路由器：一个需要认证，一个不需要认证
# 需要认证的路由器（用于管理员查看统计数据）
promotion_code_controller = APIRouter(prefix='/api/v1/promotion', dependencies=[Depends(AuthAdapter.get_current_user)])

# 不需要认证的路由器（用于记录访问）
promotion_code_public_controller = APIRouter(prefix='/api/v1/public/promotion')


@promotion_code_public_controller.get('/record-visit', summary="记录推广码访问")
async def record_promotion_code_visit(
    request: Request,
    employee_number: Optional[str] = Query(None, description="员工工号/推广码"),
    query_db: AsyncSession = Depends(get_db)
):
    """记录推广码访问接口

    当用户扫描推广码时，记录访问信息，包括员工工号、访问日期、IP地址等

    Args:
        request: FastAPI请求对象
        employee_number: 员工工号/推广码
        query_db: 数据库会话

    Returns:
        记录结果
    """
    try:
        # 调用服务层记录访问
        result = await PromotionCodeService.record_promotion_code_visit_service(
            request, query_db, employee_number
        )

        # 返回成功响应
        return ResponseUtil.success(dict_content=result)
    except (ValidationException, BusinessException, QueryException) as e:
        # 处理已知异常
        logger.error(f"记录推广码访问失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        # 处理未知异常
        error_message = f"记录推广码访问发生未知错误: {str(e)}"
        logger.error(error_message)
        return ResponseUtil.error(msg=error_message)


@promotion_code_controller.get('/statistics', summary="获取推广码访问统计")
async def get_promotion_code_statistics(
    request: Request,
    employee_number: Optional[str] = Query(None, description="员工工号/推广码（可选）"),
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD（可选）"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD（可选）"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取推广码访问统计接口

    获取推广码访问统计数据，支持按员工工号和日期范围筛选

    Args:
        request: FastAPI请求对象
        employee_number: 员工工号/推广码（可选）
        start_date: 开始日期，格式：YYYY-MM-DD（可选）
        end_date: 结束日期，格式：YYYY-MM-DD（可选）
        query_db: 数据库会话

    Returns:
        统计结果列表
    """
    try:
        # 调用服务层获取统计数据
        statistics = await PromotionCodeService.get_promotion_code_statistics_service(
            query_db, employee_number, start_date, end_date
        )

        # 返回成功响应
        return ResponseUtil.success(dict_content={"statistics": statistics})
    except (ValidationException, BusinessException, QueryException) as e:
        # 处理已知异常
        logger.error(f"获取推广码访问统计失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        # 处理未知异常
        error_message = f"获取推广码访问统计发生未知错误: {str(e)}"
        logger.error(error_message)
        return ResponseUtil.error(msg=error_message)
