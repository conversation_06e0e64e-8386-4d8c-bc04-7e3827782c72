"""
三嫂业务控制器
"""
from fastapi import APIRouter, Depends, Request, Query, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
from pydantic import BaseModel
from config.get_db import get_db
from module_admin.service.sister_business_service import SisterBusinessService
from module_admin.service.internal_user_login_service import InternalUserLoginService
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import ValidationException, BusinessException, QueryException

# 创建线索请求模型
class CreateLeadRequest(BaseModel):
    service_type: str  # aunt_type
    service_type_name: str  # aunt_name
    customer_name: str  # name
    mobile: str  # mobile
    address: str  # address
    latitude: float  # lat
    longitude: float  # lng
    city: str  # city


# 创建路由器
sister_business_controller = APIRouter(prefix='/api/v1/sister-business')


@sister_business_controller.get('/list', summary="获取三嫂业务订单列表")
async def get_sister_business_list(
    request: Request,
    store_uuid: Optional[str] = Query(None, description="门店UUID"),
    page: int = Query(1, description="页码", ge=1),
    size: int = Query(10, description="每页数量", ge=1, le=100),
    keyword: Optional[str] = Query(None, description="搜索关键词"),
    exclude_status: Optional[str] = Query(None, description="要排除的状态"),
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    获取三嫂业务订单列表

    :param request: Request对象
    :param store_uuid: 门店UUID（可选，如果不传则使用当前用户的门店）
    :param page: 页码
    :param size: 每页数量
    :param keyword: 搜索关键词
    :param exclude_status: 要排除的状态
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 订单列表
    """
    try:
        logger.info(f"获取三嫂业务订单列表，用户ID: {current_user.user.id}")
        
        # 如果没有传入store_uuid，则使用当前用户的store_uuid
        if not store_uuid:
            store_uuid = current_user.user.store_uuid
            
        if not store_uuid:
            logger.warning("当前用户没有关联的门店信息")
            raise ValidationException(message="用户没有关联的门店信息")
        
        logger.info(f"使用门店UUID: {store_uuid}")

        # 获取当前用户的角色信息用于权限控制
        current_user_role_id = current_user.user.role_id
        current_user_uuid = current_user.user.uuid

        logger.info(f"当前用户角色ID: {current_user_role_id}, 用户UUID: {current_user_uuid}")

        # 调用服务层获取订单列表
        result = await SisterBusinessService.get_sister_business_list_service(
            query_db, store_uuid, page, size, keyword, exclude_status, current_user_role_id, current_user_uuid
        )
        
        return ResponseUtil.success(
            msg="获取三嫂业务订单列表成功",
            data=result
        )
        
    except ValidationException as e:
        logger.error(f"获取三嫂业务订单列表参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取三嫂业务订单列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"获取三嫂业务订单列表查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取三嫂业务订单列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.get('/detail/{order_id}', summary="获取三嫂业务订单详情")
async def get_sister_business_detail(
    request: Request,
    order_id: str,
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    获取三嫂业务订单详情
    
    :param request: Request对象
    :param order_id: 订单ID
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 订单详情
    """
    try:
        logger.info(f"获取三嫂业务订单详情，订单ID: {order_id}, 用户ID: {current_user.user.id}")
        
        # 调用服务层获取订单详情
        result = await SisterBusinessService.get_sister_business_detail_service(
            query_db, order_id
        )
        
        return ResponseUtil.success(
            msg="获取三嫂业务订单详情成功",
            data=result
        )
        
    except ValidationException as e:
        logger.error(f"获取三嫂业务订单详情参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取三嫂业务订单详情业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"获取三嫂业务订单详情查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取三嫂业务订单详情异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.post('/update-status', summary="更新三嫂业务线索状态")
async def update_sister_business_status(
    request: Request,
    data: dict = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    更新三嫂业务线索状态并创建跟进记录

    :param request: Request对象
    :param data: 更新数据
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 更新结果
    """
    try:
        order_id = data.get('order_id')
        status = data.get('status')
        follow_content = data.get('follow_content', '')  # 改为跟进内容
        next_follow_time = data.get('next_follow_time')  # 下次跟进时间
        files = data.get('files', [])  # 获取文件信息

        # 获取用户信息（优先使用前端传递的，否则使用当前登录用户）
        # 注意：user_uuid 存储的是 getinfo 接口的 id 字段，user_name 存储的是 name 字段
        user_uuid = data.get('user_uuid') or str(current_user.user.id)
        user_name = data.get('user_name') or current_user.user.name
        status_name = data.get('status_name')

        logger.info(f"更新三嫂业务线索状态，线索ID: {order_id}, 状态: {status}, 状态名称: {status_name}, 跟进人: {user_name}, 文件数量: {len(files)}, 用户ID: {current_user.user.id}")

        # 调用客户跟进服务更新状态（支持文件）
        from module_admin.service.customer_follow_service import CustomerFollowService
        if files and len(files) > 0:
            # 有文件时调用支持文件的方法
            result = await CustomerFollowService.update_customer_status_with_follow_and_files(
                query_db, order_id, status, follow_content, next_follow_time, files,
                user_uuid, user_name, status_name
            )
        else:
            # 没有文件时调用原方法
            result = await CustomerFollowService.update_customer_status_with_follow(
                query_db, order_id, status, follow_content, next_follow_time,
                user_uuid, user_name, status_name
            )

        return ResponseUtil.success(
            msg="更新三嫂业务线索状态成功",
            data=result
        )
        
    except ValidationException as e:
        logger.error(f"更新三嫂业务订单状态参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"更新三嫂业务订单状态业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"更新三嫂业务订单状态查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"更新三嫂业务订单状态异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.get('/statistics', summary="获取三嫂业务统计信息")
async def get_sister_business_statistics(
    request: Request,
    store_uuid: Optional[str] = Query(None, description="门店UUID"),
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    获取三嫂业务统计信息
    
    :param request: Request对象
    :param store_uuid: 门店UUID（可选，如果不传则使用当前用户的门店）
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 统计信息
    """
    try:
        logger.info(f"获取三嫂业务统计信息，用户ID: {current_user.user.id}")
        
        # 如果没有传入store_uuid，则使用当前用户的store_uuid
        if not store_uuid:
            store_uuid = current_user.user.store_uuid
            
        if not store_uuid:
            logger.warning("当前用户没有关联的门店信息")
            raise ValidationException(message="用户没有关联的门店信息")
        
        logger.info(f"使用门店UUID: {store_uuid}")

        # 获取当前用户的角色信息用于权限控制
        current_user_role_id = current_user.user.role_id
        current_user_uuid = current_user.user.uuid

        logger.info(f"当前用户角色ID: {current_user_role_id}, 用户UUID: {current_user_uuid}")

        # 调用服务层获取统计信息
        result = await SisterBusinessService.get_sister_business_statistics_service(
            query_db, store_uuid, current_user_role_id, current_user_uuid
        )
        
        return ResponseUtil.success(
            msg="获取三嫂业务统计信息成功",
            data=result
        )
        
    except ValidationException as e:
        logger.error(f"获取三嫂业务统计信息参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取三嫂业务统计信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"获取三嫂业务统计信息查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取三嫂业务统计信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.get('/follow-records/{customer_uuid}', summary="获取客户跟进记录")
async def get_customer_follow_records(
    request: Request,
    customer_uuid: str,
    page: int = Query(1, description="页码", ge=1),
    size: int = Query(10, description="每页数量", ge=1, le=100),
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    获取客户跟进记录

    :param request: Request对象
    :param customer_uuid: 客户UUID
    :param page: 页码
    :param size: 每页数量
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 跟进记录列表
    """
    try:
        logger.info(f"获取客户跟进记录，客户UUID: {customer_uuid}, 用户ID: {current_user.user.id}")

        # 调用客户跟进服务获取记录
        from module_admin.service.customer_follow_service import CustomerFollowService
        result = await CustomerFollowService.get_customer_follow_list(
            query_db, customer_uuid, page, size
        )

        return ResponseUtil.success(
            msg="获取客户跟进记录成功",
            data=result["data"]
        )

    except ValidationException as e:
        logger.error(f"获取客户跟进记录参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取客户跟进记录业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"获取客户跟进记录查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取客户跟进记录异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.get('/completed-customers', summary="获取已成交客户列表")
async def get_completed_customers(
    request: Request,
    store_uuid: Optional[str] = Query(None, description="门店UUID"),
    page: int = Query(1, description="页码", ge=1),
    size: int = Query(10, description="每页数量", ge=1, le=100),
    keyword: Optional[str] = Query(None, description="搜索关键词"),
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    获取已成交客户列表

    :param request: Request对象
    :param store_uuid: 门店UUID（可选，如果不传则使用当前用户的门店）
    :param page: 页码
    :param size: 每页数量
    :param keyword: 搜索关键词
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 已成交客户列表
    """
    try:
        logger.info(f"获取已成交客户列表，用户ID: {current_user.user.id}")

        # 如果没有传入store_uuid，则使用当前用户的store_uuid
        if not store_uuid:
            store_uuid = current_user.user.store_uuid

        if not store_uuid:
            logger.warning("当前用户没有关联的门店信息")
            raise ValidationException(message="用户没有关联的门店信息")

        logger.info(f"使用门店UUID: {store_uuid}")

        # 调用服务层获取已成交客户列表
        result = await SisterBusinessService.get_completed_customers_service(
            query_db, store_uuid, page, size, keyword
        )

        return ResponseUtil.success(
            msg="获取已成交客户列表成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取已成交客户列表参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取已成交客户列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"获取已成交客户列表查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取已成交客户列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.get('/customer-detail/{customer_uuid}', summary="获取完整的客户资料信息")
async def get_customer_detail(
    request: Request,
    customer_uuid: str,
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    获取完整的客户资料信息

    :param request: Request对象
    :param customer_uuid: 客户UUID
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 完整的客户资料
    """
    try:
        logger.info(f"获取客户资料详情，客户UUID: {customer_uuid}, 用户ID: {current_user.user.id}")

        # 调用服务层获取客户详情
        result = await SisterBusinessService.get_customer_detail_service(
            query_db, customer_uuid
        )

        return ResponseUtil.success(
            msg="获取客户资料详情成功",
            data=result
        )

    except ValidationException as e:
        logger.error(f"获取客户资料详情参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取客户资料详情业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"获取客户资料详情查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取客户资料详情异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.post('/update-customer', summary="更新客户资料信息")
async def update_customer_detail(
    request: Request,
    data: dict = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    更新客户资料信息

    :param request: Request对象
    :param data: 客户资料数据
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 更新结果
    """
    try:
        customer_uuid = data.get('uuid')

        if not customer_uuid:
            raise ValidationException(message="客户UUID不能为空")

        logger.info(f"更新客户资料，客户UUID: {customer_uuid}, 用户ID: {current_user.user.id}")

        # 调用服务层更新客户资料
        result = await SisterBusinessService.update_customer_detail_service(
            query_db, customer_uuid, data, current_user
        )

        return ResponseUtil.success(
            msg="更新客户资料成功",
            data=result
        )

    except ValidationException as e:
        logger.error(f"更新客户资料参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"更新客户资料业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"更新客户资料查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"更新客户资料异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.get('/source-list', summary="获取来源列表")
async def get_source_list(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    获取来源列表

    :param request: Request对象
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 来源列表
    """
    try:
        logger.info(f"获取来源列表，用户ID: {current_user.user.id}")

        # 调用服务层获取来源列表
        result = await SisterBusinessService.get_source_list_service(query_db)

        return ResponseUtil.success(
            msg="获取来源列表成功",
            data=result
        )

    except ValidationException as e:
        logger.error(f"获取来源列表参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取来源列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"获取来源列表查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取来源列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.get('/customer-tags', summary="获取客户标签列表")
async def get_customer_tags(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    获取客户标签列表

    :param request: Request对象
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 标签列表
    """
    try:
        logger.info(f"获取客户标签列表，用户ID: {current_user.user.id}")

        # 调用服务层获取标签列表
        result = await SisterBusinessService.get_customer_tags_service(query_db)

        return ResponseUtil.success(
            msg="获取客户标签列表成功",
            data=result
        )

    except ValidationException as e:
        logger.error(f"获取客户标签列表参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取客户标签列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"获取客户标签列表查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取客户标签列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.post('/add-customer-tags', summary="添加客户标签关联")
async def add_customer_tags(
    request: Request,
    data: dict = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    添加客户标签关联

    :param request: Request对象
    :param data: 标签关联数据
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 添加结果
    """
    try:
        customer_uuid = data.get('customer_uuid')
        tag_ids = data.get('tag_ids', [])

        if not customer_uuid:
            raise ValidationException(message="客户UUID不能为空")

        if not tag_ids or not isinstance(tag_ids, list):
            raise ValidationException(message="标签ID列表不能为空")

        logger.info(f"添加客户标签关联，客户UUID: {customer_uuid}, 标签IDs: {tag_ids}, 用户ID: {current_user.user.id}")

        # 调用服务层添加标签关联
        result = await SisterBusinessService.add_customer_tags_service(
            query_db, customer_uuid, tag_ids, current_user.user.id
        )

        return ResponseUtil.success(
            msg="添加客户标签关联成功",
            data=result
        )

    except ValidationException as e:
        logger.error(f"添加客户标签关联参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"添加客户标签关联业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"添加客户标签关联查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"添加客户标签关联异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.post('/remove-customer-tag', summary="删除客户标签关联")
async def remove_customer_tag(
    request: Request,
    data: dict = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    删除客户标签关联

    :param request: Request对象
    :param data: 删除数据
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 删除结果
    """
    try:
        customer_uuid = data.get('customer_uuid')
        tag_id = data.get('tag_id')

        if not customer_uuid:
            raise ValidationException(message="客户UUID不能为空")

        if not tag_id:
            raise ValidationException(message="标签ID不能为空")

        logger.info(f"删除客户标签关联，客户UUID: {customer_uuid}, 标签ID: {tag_id}, 用户ID: {current_user.user.id}")

        # 调用服务层删除标签关联
        result = await SisterBusinessService.remove_customer_tag_service(
            query_db, customer_uuid, tag_id, current_user.user.id
        )

        return ResponseUtil.success(
            msg="删除客户标签关联成功",
            data=result
        )

    except ValidationException as e:
        logger.error(f"删除客户标签关联参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"删除客户标签关联业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"删除客户标签关联查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"删除客户标签关联异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.post('/create-customer-tag', summary="创建新的客户标签")
async def create_customer_tag(
    request: Request,
    data: dict = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    创建新的客户标签

    :param request: Request对象
    :param data: 标签数据
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 创建结果
    """
    try:
        name = data.get('name')
        color = data.get('color', '#1890FF')

        if not name or not name.strip():
            raise ValidationException(message="标签名称不能为空")

        # 验证颜色格式
        if not color.startswith('#') or len(color) != 7:
            raise ValidationException(message="标签颜色格式不正确")

        logger.info(f"创建客户标签，名称: {name}, 颜色: {color}, 用户ID: {current_user.user.id}")

        # 调用服务层创建标签
        result = await SisterBusinessService.create_customer_tag_service(
            query_db, name.strip(), color, current_user.user.id
        )

        return ResponseUtil.success(
            msg="创建客户标签成功",
            data=result
        )

    except ValidationException as e:
        logger.error(f"创建客户标签参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"创建客户标签业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"创建客户标签查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"创建客户标签异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.post('/generate-share-link', summary="生成线索分享链接")
async def generate_share_link(
    request: Request,
    data: dict = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    生成线索分享链接

    :param request: Request对象
    :param data: 包含customer_uuid的数据
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 分享链接信息
    """
    try:
        customer_uuid = data.get('customer_uuid')

        if not customer_uuid:
            raise ValidationException(message="客户UUID不能为空")

        logger.info(f"生成线索分享链接，客户UUID: {customer_uuid}, 用户ID: {current_user.user.id}")

        # 调用服务层生成分享链接
        result = await SisterBusinessService.generate_share_link_service(
            query_db, customer_uuid, current_user.user.id
        )

        return ResponseUtil.success(
            msg="生成分享链接成功",
            data=result
        )

    except ValidationException as e:
        logger.error(f"生成分享链接参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"生成分享链接业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"生成分享链接查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"生成分享链接异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.get('/share-lead-info/{share_token}', summary="获取分享线索信息")
async def get_share_lead_info(
    request: Request,
    share_token: str,
    query_db: AsyncSession = Depends(get_db)
):
    """
    根据分享token获取线索信息（无需登录）

    :param request: Request对象
    :param share_token: 分享token
    :param query_db: 数据库会话
    :return: 线索信息
    """
    try:
        logger.info(f"获取分享线索信息，token: {share_token}")

        # 调用服务层获取分享线索信息
        result = await SisterBusinessService.get_share_lead_info_service(
            query_db, share_token
        )

        return ResponseUtil.success(
            msg="获取分享线索信息成功",
            data=result
        )

    except ValidationException as e:
        logger.error(f"获取分享线索信息参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取分享线索信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"获取分享线索信息查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取分享线索信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.post('/apply-for-order', summary="申请接单")
async def apply_for_order(
    request: Request,
    data: dict = Body(...),
    query_db: AsyncSession = Depends(get_db)
):
    """
    处理接单申请（无需登录）

    :param request: Request对象
    :param data: 申请数据，包含share_token、wx_code、encrypted_data、iv
    :param query_db: 数据库会话
    :return: 申请结果
    """
    try:
        share_token = data.get('share_token')
        wx_code = data.get('wx_code')
        encrypted_data = data.get('encrypted_data')
        iv = data.get('iv')

        if not share_token:
            raise ValidationException(message="分享token不能为空")

        if not wx_code:
            raise ValidationException(message="微信授权码不能为空")

        logger.info(f"处理接单申请，token: {share_token}, wx_code: {wx_code}")

        # 调用服务层处理接单申请
        result = await SisterBusinessService.apply_for_order_service(
            query_db, share_token, wx_code, encrypted_data, iv
        )

        return ResponseUtil.success(
            msg="申请接单成功",
            data=result
        )

    except ValidationException as e:
        logger.error(f"申请接单参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"申请接单业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"申请接单查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"申请接单异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.get('/customer-applications/{customer_uuid}', summary="获取客户申请接单记录")
async def get_customer_applications(
    customer_uuid: str,
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    获取客户申请接单记录
    """
    try:
        logger.info(f"获取客户申请记录，客户UUID: {customer_uuid}, 用户ID: {current_user.user.id}")

        # 调用服务层获取申请记录
        applications = await SisterBusinessService.get_customer_applications_service(
            query_db, customer_uuid
        )

        return ResponseUtil.success(data=applications)

    except QueryException as e:
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取客户申请记录异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.post('/accept-application', summary="商家接受申请")
async def accept_application(
    request: Request,
    data: dict = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    商家接受申请（选择阿姨）
    """
    try:
        customer_uuid = data.get('customer_uuid')
        aunt_uuid = data.get('aunt_uuid')

        if not customer_uuid or not aunt_uuid:
            return ResponseUtil.error(message="客户UUID和阿姨UUID不能为空")

        logger.info(f"商家接受申请，客户UUID: {customer_uuid}, 阿姨UUID: {aunt_uuid}, 用户ID: {current_user.user.id}")

        # 调用服务层处理接受申请
        success = await SisterBusinessService.accept_customer_application_service(
            query_db, customer_uuid, aunt_uuid, current_user.user.uuid, current_user.user.nick_name
        )

        if success:
            return ResponseUtil.success(data={"success": True, "message": "选择阿姨成功"})
        else:
            return ResponseUtil.error(message="选择阿姨失败")

    except QueryException as e:
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"商家接受申请异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.post('/create-interview', summary="创建面试安排")
async def create_interview(
    request: Request,
    data: dict = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    创建面试安排
    """
    try:
        customer_uuid = data.get('customer_uuid')
        aunt_uuid = data.get('aunt_uuid')
        interview_time = data.get('interview_time')

        if not customer_uuid or not aunt_uuid or not interview_time:
            raise ValidationException(message="客户UUID、阿姨UUID和面试时间不能为空")

        logger.info(f"创建面试安排，客户UUID: {customer_uuid}, 阿姨UUID: {aunt_uuid}, 用户ID: {current_user.user.id}")

        # 调用服务层创建面试安排
        result = await SisterBusinessService.create_interview_service(
            query_db, data, current_user.user.uuid, current_user.user.nick_name
        )

        return ResponseUtil.success(
            msg="创建面试安排成功",
            data=result
        )

    except ValidationException as e:
        logger.error(f"创建面试安排参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"创建面试安排业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"创建面试安排查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"创建面试安排异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.post('/create-lead', summary="创建新线索")
async def create_lead(
    request: Request,
    lead_data: CreateLeadRequest,
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    创建新线索

    :param request: Request对象
    :param lead_data: 线索数据
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 创建结果
    """
    try:
        logger.info(f"创建新线索，用户ID: {current_user.user.id}, 客户姓名: {lead_data.customer_name}")

        # 调用服务层创建线索
        result = await SisterBusinessService.create_lead_service(
            query_db, lead_data.model_dump(), current_user.user.store_uuid, current_user.user
        )

        return ResponseUtil.success(
            msg="创建线索成功",
            data=result
        )

    except ValidationException as e:
        logger.error(f"创建线索参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"创建线索业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"创建线索查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"创建线索异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.get('/interviews/{customer_uuid}', summary="获取客户面试记录")
async def get_interviews(
    customer_uuid: str,
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    获取客户面试记录
    """
    try:
        logger.info(f"获取面试记录，客户UUID: {customer_uuid}, 用户ID: {current_user.user.id}")

        # 调用服务层获取面试记录
        interviews = await SisterBusinessService.get_interviews_service(
            query_db, customer_uuid
        )

        return ResponseUtil.success(data=interviews)

    except ValidationException as e:
        logger.error(f"获取面试记录参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取面试记录业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"获取面试记录查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取面试记录异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.put('/interview/{interview_uuid}', summary="更新面试信息")
async def update_interview(
    interview_uuid: str,
    data: dict = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    更新面试信息
    """
    try:
        logger.info(f"更新面试信息，面试UUID: {interview_uuid}, 用户ID: {current_user.user.id}")

        # 调用服务层更新面试信息
        result = await SisterBusinessService.update_interview_service(
            query_db, interview_uuid, data
        )

        return ResponseUtil.success(
            msg="更新面试信息成功",
            data=result
        )

    except ValidationException as e:
        logger.error(f"更新面试信息参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"更新面试信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"更新面试信息查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"更新面试信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.post('/interview/{interview_uuid}/cancel', summary="取消面试")
async def cancel_interview(
    interview_uuid: str,
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    取消面试
    """
    try:
        logger.info(f"取消面试，面试UUID: {interview_uuid}, 用户ID: {current_user.user.id}")

        # 调用服务层取消面试
        result = await SisterBusinessService.cancel_interview_service(
            query_db, interview_uuid
        )

        return ResponseUtil.success(
            msg="取消面试成功",
            data=result
        )

    except ValidationException as e:
        logger.error(f"取消面试参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"取消面试业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"取消面试查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"取消面试异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@sister_business_controller.post('/set-accepted-aunt', summary="设置线索的接单人员")
async def set_accepted_aunt(
    data: dict = Body(...),
    query_db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """
    设置线索的接单人员

    :param data: 接单人员数据，包含customer_uuid、aunt_uuid、aunt_name
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 设置结果
    """
    try:
        customer_uuid = data.get('customer_uuid')
        aunt_uuid = data.get('aunt_uuid')
        aunt_name = data.get('aunt_name')

        if not customer_uuid or not aunt_uuid or not aunt_name:
            raise ValidationException(message="客户UUID、阿姨UUID和阿姨姓名不能为空")

        logger.info(f"设置线索接单人员，客户UUID: {customer_uuid}, 阿姨UUID: {aunt_uuid}, 用户ID: {current_user.user.id}")

        # 调用服务层设置接单人员
        result = await SisterBusinessService.set_accepted_aunt_service(
            query_db, customer_uuid, aunt_uuid, aunt_name, current_user
        )

        return ResponseUtil.success(data=result)

    except ValidationException as e:
        logger.error(f"设置接单人员参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"设置接单人员业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"设置接单人员查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"设置接单人员异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
