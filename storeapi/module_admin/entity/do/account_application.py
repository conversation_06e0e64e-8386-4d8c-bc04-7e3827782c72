from sqlalchemy import Column, String, DECIMAL, DateTime, BigInteger, Text
from sqlalchemy.sql import func
from config.database import Base


class AccountApplication(Base):
    """开户申请信息表"""

    __tablename__ = 'account_application'

    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    uuid = Column(String(64), nullable=False, unique=True, comment='申请UUID')
    
    # 基本信息
    mobile = Column(String(20), nullable=False, index=True, comment='手机号')
    name = Column(String(50), nullable=True, comment='姓名')
    id_number = Column(String(18), nullable=True, comment='身份证号')
    gender = Column(String(2), nullable=True, comment='性别(1:男,2:女)')
    birthday = Column(String(20), nullable=True, comment='出生日期')
    
    # 地址信息
    province = Column(String(50), nullable=True, comment='省份')
    city = Column(String(50), nullable=True, comment='城市')
    district = Column(String(50), nullable=True, comment='区县')
    province_code = Column(String(50), nullable=True, comment='省份编码')
    city_code = Column(String(50), nullable=True, comment='城市编码')
    district_code = Column(String(50), nullable=True, comment='区县编码')
    address = Column(String(255), nullable=True, comment='详细地址')
    
    # 职业信息
    occupation = Column(String(100), nullable=True, comment='职业')
    company_name = Column(String(200), nullable=True, comment='工作单位')
    annual_income = Column(DECIMAL(15, 2), nullable=True, comment='年收入')
    
    # 银行信息
    bank_name = Column(String(100), nullable=True, comment='开户银行')
    bank_code = Column(String(50), nullable=True, comment='银行代码')
    account_no = Column(String(50), nullable=True, comment='银行账号')
    account_name = Column(String(100), nullable=True, comment='账户名称')
    account_type = Column(String(20), nullable=True, default='pCard', comment='账户类型(pCard:个人借记卡,eGeneral:对公一般户,eCard:企业单位结算卡)')
    
    # 证件信息
    id_card_front_url = Column(String(500), nullable=True, comment='身份证正面照片URL')
    id_card_back_url = Column(String(500), nullable=True, comment='身份证背面照片URL')
    bank_card_url = Column(String(500), nullable=True, comment='银行卡照片URL')
    issuing_authority = Column(String(100), nullable=True, comment='签发机关')
    validity_period = Column(String(50), nullable=True, comment='有效期限')
    
    # 易宝支付相关
    yeepay_customer_code = Column(String(64), nullable=True, comment='易宝入网客户代码')
    yeepay_cus_trace_no = Column(String(64), nullable=True, comment='易宝客户请求流水号')
    yeepay_sys_trace_no = Column(String(64), nullable=True, comment='易宝系统跟踪号')
    yeepay_txn_date = Column(String(8), nullable=True, comment='易宝交易日期(yyyyMMdd)')
    yeepay_rsp_time = Column(String(14), nullable=True, comment='易宝响应时间(yyyyMMddHHmmss)')

    # 申请状态
    status = Column(String(2), nullable=False, default='0', index=True, comment='申请状态(0:待审核,1:审核中,2:已通过,3:已拒绝)')
    remark = Column(String(500), nullable=True, comment='备注')
    
    # 时间信息
    apply_time = Column(DateTime, nullable=False, default=func.now(), index=True, comment='申请时间')
    review_time = Column(DateTime, nullable=True, comment='审核时间')
    reviewer_id = Column(String(64), nullable=True, comment='审核人ID')
    
    # 系统字段
    create_time = Column(DateTime, nullable=False, default=func.now(), comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now(), comment='更新时间')
