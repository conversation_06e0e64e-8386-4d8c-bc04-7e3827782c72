from fastapi import APIRouter, Depends, Request, Query, Form, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any
from config.get_db import get_db
from module_admin.service.recruit_service import RecruitService
from module_admin.service.auth_adapter import AuthAdapter
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, ResourceNotFoundException, BusinessException, ValidationException

# 使用统一的API路由前缀格式
recruit_controller = APIRouter(prefix='/api/v1/recruit', dependencies=[Depends(AuthAdapter.get_current_user)])

@recruit_controller.post('/orderStatistics', summary="获取订单统计信息")
async def get_order_statistics(
    request: Request,
    user_uuid: Optional[str] = Form(None, description="用户UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取订单统计信息接口
    
    获取招聘订单的统计信息，包括开启数量、关闭数量和总数
    
    原始API路径: /recruitV2/orderStatistics
    """
    try:
        # 调用服务层获取订单统计信息
        result = await RecruitService.get_order_statistics_service(query_db, user_uuid)
        
        return ResponseUtil.success(
            msg="获取订单统计信息成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询订单统计信息失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取订单统计信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取订单统计信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@recruit_controller.post('/findRecruitOrderList', summary="获取招聘订单列表")
async def find_recruit_order_list(
    request: Request,
    page: int = Form(1, description="页码"),
    size: int = Form(20, description="每页数量"),
    status: Optional[str] = Form(None, description="状态"),
    demand_uuid: Optional[str] = Form(None, description="需求UUID"),
    user_uuid: Optional[str] = Form(None, description="用户UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取招聘订单列表接口
    
    分页获取招聘订单列表，支持按状态、需求UUID和用户UUID筛选
    
    原始API路径: /recruitV2/findRecruitOrderList
    """
    try:
        # 调用服务层获取招聘订单列表
        result = await RecruitService.find_recruit_order_list_service(
            query_db, page, size, status, demand_uuid, user_uuid
        )
        
        return ResponseUtil.success(
            msg="获取招聘订单列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询招聘订单列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取招聘订单列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取招聘订单列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@recruit_controller.post('/addRecruitmentOrder', summary="添加招聘订单")
async def add_recruitment_order(
    request: Request,
    demand_uuid: str = Form(..., description="需求UUID"),
    demand_number: str = Form(..., description="需求编号"),
    user_uuid: str = Form(..., description="用户UUID"),
    on_off: str = Form("1", description="开关状态"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """添加招聘订单接口
    
    创建新的招聘订单
    
    原始API路径: /recruit/addRecruitmentOrder
    """
    try:
        # 构建订单数据
        order_data = {
            "demand_uuid": demand_uuid,
            "demand_number": demand_number,
            "user_uuid": user_uuid,
            "on_off": on_off
        }
        
        # 调用服务层添加招聘订单
        result = await RecruitService.add_recruitment_order_service(query_db, order_data)
        
        return ResponseUtil.success(
            msg="添加招聘订单成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"添加招聘订单数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"添加招聘订单业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"添加招聘订单异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@recruit_controller.post('/setRecruitmentOrderOnOff', summary="设置招聘订单开关状态")
async def set_recruitment_order_on_off(
    request: Request,
    uuid: str = Form(..., description="订单UUID"),
    on_off: str = Form(..., description="开关状态"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """设置招聘订单开关状态接口
    
    更新招聘订单的开关状态
    
    原始API路径: /recruit/setRecruitmentOrderOnOff
    """
    try:
        # 调用服务层设置招聘订单开关状态
        result = await RecruitService.set_recruitment_order_on_off_service(query_db, uuid, on_off)
        
        return ResponseUtil.success(
            msg="设置招聘订单开关状态成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"设置招聘订单开关状态数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"设置招聘订单开关状态业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"设置招聘订单开关状态异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@recruit_controller.post('/sharedRecruitOrder', summary="共享招聘订单")
async def shared_recruit_order(
    request: Request,
    order_uuid: str = Form(..., description="订单UUID"),
    share_user_uuid: str = Form(..., description="分享用户UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """共享招聘订单接口
    
    将招聘订单共享给其他用户
    
    原始API路径: /recruitV2/sharedRecruitOrder
    """
    try:
        # 调用服务层共享招聘订单
        result = await RecruitService.shared_recruit_order_service(query_db, order_uuid, share_user_uuid)
        
        return ResponseUtil.success(
            msg="共享招聘订单成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"共享招聘订单数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"共享招聘订单业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"共享招聘订单异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
