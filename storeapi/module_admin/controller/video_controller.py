from fastapi import APIRouter, Depends, Request, Query, Form, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any
from config.get_db import get_db
from module_admin.service.video_service import VideoService
from module_admin.service.auth_adapter import AuthAdapter
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, ResourceNotFoundException, BusinessException, ValidationException

# 使用统一的API路由前缀格式
video_controller = APIRouter(prefix='/api/v1/video', dependencies=[Depends(AuthAdapter.get_current_user)])

@video_controller.get('/getList', summary="获取短视频列表")
async def get_video_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    type: Optional[str] = Query(None, description="视频类型"),
    keyword: Optional[str] = Query(None, description="关键词"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取短视频列表接口
    
    分页获取短视频列表，支持按类型和关键词筛选
    
    原始API路径: /videov3/getList
    """
    try:
        # 调用服务层获取短视频列表
        result = await VideoService.get_video_list_service(query_db, page, size, type, keyword)
        
        return ResponseUtil.success(
            msg="获取短视频列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询短视频列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取短视频列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取短视频列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@video_controller.get('/getOne', summary="获取单个短视频信息")
async def get_video_one(
    request: Request,
    uuid: str = Query(..., description="视频UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取单个短视频信息接口
    
    根据UUID获取视频的详细信息
    
    原始API路径: /videov3/getOne
    """
    try:
        # 调用服务层获取单个短视频信息
        result = await VideoService.get_video_one_service(query_db, uuid)
        
        return ResponseUtil.success(
            msg="获取单个短视频信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取单个短视频信息数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"视频不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询单个短视频信息失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取单个短视频信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取单个短视频信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@video_controller.post('/save', summary="保存短视频信息")
async def save_video(
    request: Request,
    uuid: Optional[str] = Form(None, description="视频UUID"),
    title: str = Form(..., description="标题"),
    description: Optional[str] = Form("", description="描述"),
    type: str = Form(..., description="类型"),
    video_url: str = Form(..., description="视频URL"),
    cover_url: Optional[str] = Form("", description="封面URL"),
    share_title: Optional[str] = Form("", description="分享标题"),
    share_description: Optional[str] = Form("", description="分享描述"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """保存短视频信息接口
    
    保存短视频信息，包括基本信息和分享信息
    
    原始API路径: /videov3/save
    """
    try:
        # 获取当前用户信息
        current_user = await AuthAdapter.get_current_user(request)
        
        # 构建视频数据
        video_data = {
            "uuid": uuid,
            "title": title,
            "description": description,
            "type": type,
            "video_url": video_url,
            "cover_url": cover_url,
            "share_title": share_title,
            "share_description": share_description,
            "creator_uuid": current_user.get("user_uuid")
        }
        
        # 调用服务层保存短视频信息
        result = await VideoService.save_video_service(query_db, video_data)
        
        return ResponseUtil.success(
            msg="保存短视频信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"保存短视频信息数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"保存短视频信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"保存短视频信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@video_controller.post('/delete', summary="删除短视频")
async def delete_video(
    request: Request,
    uuid: str = Form(..., description="视频UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """删除短视频接口
    
    根据UUID删除短视频
    
    原始API路径: /videov3/delete
    """
    try:
        # 调用服务层删除短视频
        result = await VideoService.delete_video_service(query_db, uuid)
        
        return ResponseUtil.success(
            msg="删除短视频成功",
            data={"success": result}
        )
    except ValidationException as e:
        logger.error(f"删除短视频数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"视频不存在或删除失败: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"删除短视频业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"删除短视频异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@video_controller.get('/getShareList', summary="获取短视频分享列表")
async def get_video_share_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    video_uuid: Optional[str] = Query(None, description="视频UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取短视频分享列表接口
    
    分页获取短视频分享列表，支持按视频UUID筛选
    
    原始API路径: /videov3/getShareList
    """
    try:
        # 调用服务层获取短视频分享列表
        result = await VideoService.get_video_share_list_service(query_db, page, size, video_uuid)
        
        return ResponseUtil.success(
            msg="获取短视频分享列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询短视频分享列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取短视频分享列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取短视频分享列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@video_controller.post('/saveShare', summary="保存短视频分享信息")
async def save_video_share(
    request: Request,
    video_uuid: str = Form(..., description="视频UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """保存短视频分享信息接口
    
    记录用户分享短视频的信息
    
    原始API路径: /videov3/saveShare
    """
    try:
        # 获取当前用户信息
        current_user = await AuthAdapter.get_current_user(request)
        
        # 构建分享数据
        share_data = {
            "video_uuid": video_uuid,
            "user_uuid": current_user.get("user_uuid")
        }
        
        # 调用服务层保存短视频分享信息
        result = await VideoService.save_video_share_service(query_db, share_data)
        
        return ResponseUtil.success(
            msg="保存短视频分享信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"保存短视频分享信息数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"保存短视频分享信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"保存短视频分享信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@video_controller.get('/getVisitorData', summary="获取短视频访问者数据")
async def get_video_visitor_data(
    request: Request,
    video_uuid: str = Query(..., description="视频UUID"),
    start_date: str = Query(..., description="开始日期"),
    end_date: str = Query(..., description="结束日期"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取短视频访问者数据接口
    
    根据视频UUID和日期范围获取短视频访问者数据
    
    原始API路径: /videov3/getVisitorData
    """
    try:
        # 调用服务层获取短视频访问者数据
        result = await VideoService.get_video_visitor_data_service(query_db, video_uuid, start_date, end_date)
        
        return ResponseUtil.success(
            msg="获取短视频访问者数据成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取短视频访问者数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询短视频访问者数据失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取短视频访问者数据业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取短视频访问者数据异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@video_controller.get('/view', summary="短视频查看")
async def video_view(
    request: Request,
    video_uuid: str = Query(..., description="视频UUID"),
    visitor_uuid: Optional[str] = Query(None, description="访问者UUID"),
    share_uuid: Optional[str] = Query(None, description="分享UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """短视频查看接口
    
    查看短视频详情，并记录访问记录
    
    原始API路径: /videov3/view
    """
    try:
        # 调用服务层短视频查看
        result = await VideoService.video_view_service(query_db, video_uuid, visitor_uuid, share_uuid)
        
        return ResponseUtil.success(
            msg="短视频查看成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"短视频查看数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"视频不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"短视频查看查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"短视频查看业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"短视频查看异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
