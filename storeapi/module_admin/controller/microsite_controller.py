from fastapi import APIRouter, Depends, Request, Query, Form, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any, List
from config.get_db import get_db
from module_admin.service.microsite_service import MicrositeService
from module_admin.service.auth_adapter import AuthAdapter
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, ResourceNotFoundException, BusinessException, ValidationException

# 使用统一的API路由前缀格式
microsite_controller = APIRouter(prefix='/api/v1/microsite', dependencies=[Depends(AuthAdapter.get_current_user)])

@microsite_controller.get('/getStoreWebsiteImageList', summary="获取店铺网站图片列表")
async def get_store_website_image_list(
    request: Request,
    store_uuid: str = Query(..., description="店铺UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取店铺网站图片列表接口
    
    根据店铺UUID获取网站图片列表
    
    原始API路径: /micrositev3/getStoreWebsiteImageList
    """
    try:
        # 调用服务层获取店铺网站图片列表
        result = await MicrositeService.get_store_website_image_list_service(query_db, store_uuid)
        
        return ResponseUtil.success(
            msg="获取店铺网站图片列表成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取店铺网站图片列表数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询店铺网站图片列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取店铺网站图片列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取店铺网站图片列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@microsite_controller.get('/getMicroSiteShareQrUrl', summary="获取微站点分享二维码URL")
async def get_micro_site_share_qr_url(
    request: Request,
    store_uuid: str = Query(..., description="店铺UUID"),
    type: str = Query(..., description="类型"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取微站点分享二维码URL接口
    
    根据店铺UUID和类型获取微站点分享二维码URL
    
    原始API路径: /micrositev3/getMicroSiteShareQrUrl
    """
    try:
        # 调用服务层获取微站点分享二维码URL
        result = await MicrositeService.get_micro_site_share_qr_url_service(query_db, store_uuid, type)
        
        return ResponseUtil.success(
            msg="获取微站点分享二维码URL成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取微站点分享二维码URL数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询微站点分享二维码URL失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取微站点分享二维码URL业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取微站点分享二维码URL异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@microsite_controller.post('/saveStoreWebsiteV2', summary="保存店铺网站V2")
async def save_store_website_v2(
    request: Request,
    store_uuid: str = Form(..., description="店铺UUID"),
    title: str = Form(..., description="标题"),
    description: Optional[str] = Form("", description="描述"),
    logo: Optional[str] = Form("", description="Logo"),
    banner: Optional[str] = Form("", description="Banner"),
    contact_name: Optional[str] = Form("", description="联系人姓名"),
    contact_mobile: Optional[str] = Form("", description="联系人手机号"),
    address: Optional[str] = Form("", description="地址"),
    lng: Optional[str] = Form("", description="经度"),
    lat: Optional[str] = Form("", description="纬度"),
    images: Optional[List[str]] = Form([], description="图片列表"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """保存店铺网站V2接口
    
    保存店铺网站信息，包括基本信息和图片列表
    
    原始API路径: /micrositev3/saveStoreWebsiteV2
    """
    try:
        # 构建网站数据
        website_data = {
            "store_uuid": store_uuid,
            "title": title,
            "description": description,
            "logo": logo,
            "banner": banner,
            "contact_name": contact_name,
            "contact_mobile": contact_mobile,
            "address": address,
            "lng": lng,
            "lat": lat,
            "images": images
        }
        
        # 调用服务层保存店铺网站V2
        result = await MicrositeService.save_store_website_v2_service(query_db, website_data)
        
        return ResponseUtil.success(
            msg="保存店铺网站V2成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"保存店铺网站V2数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"保存店铺网站V2业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"保存店铺网站V2异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@microsite_controller.get('/getStoreWebsiteV2', summary="获取店铺网站V2")
async def get_store_website_v2(
    request: Request,
    store_uuid: str = Query(..., description="店铺UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取店铺网站V2接口
    
    根据店铺UUID获取网站信息
    
    原始API路径: /micrositev3/getStoreWebsiteV2
    """
    try:
        # 调用服务层获取店铺网站V2
        result = await MicrositeService.get_store_website_v2_service(query_db, store_uuid)
        
        return ResponseUtil.success(
            msg="获取店铺网站V2成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取店铺网站V2数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询店铺网站V2失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取店铺网站V2业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取店铺网站V2异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@microsite_controller.get('/getMicroSiteVisitorData', summary="获取微站访问者数据")
async def get_micro_site_visitor_data(
    request: Request,
    store_uuid: str = Query(..., description="店铺UUID"),
    start_date: str = Query(..., description="开始日期"),
    end_date: str = Query(..., description="结束日期"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取微站访问者数据接口
    
    根据店铺UUID和日期范围获取微站访问者数据
    
    原始API路径: /micrositev3/getMicroSiteVisitorData
    """
    try:
        # 调用服务层获取微站访问者数据
        result = await MicrositeService.get_micro_site_visitor_data_service(query_db, store_uuid, start_date, end_date)
        
        return ResponseUtil.success(
            msg="获取微站访问者数据成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取微站访问者数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询微站访问者数据失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取微站访问者数据业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取微站访问者数据异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@microsite_controller.get('/getMicroShareUserDetail', summary="获取微站分享用户详情")
async def get_micro_share_user_detail(
    request: Request,
    share_uuid: str = Query(..., description="分享UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取微站分享用户详情接口
    
    根据分享UUID获取微站分享用户详情
    
    原始API路径: /micrositev3/getMicroShareUserDetail
    """
    try:
        # 调用服务层获取微站分享用户详情
        result = await MicrositeService.get_micro_share_user_detail_service(query_db, share_uuid)
        
        return ResponseUtil.success(
            msg="获取微站分享用户详情成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取微站分享用户详情数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"分享记录不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询微站分享用户详情失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取微站分享用户详情业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取微站分享用户详情异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@microsite_controller.get('/storeHome', summary="店铺首页")
async def store_home(
    request: Request,
    store_uuid: str = Query(..., description="店铺UUID"),
    visitor_uuid: Optional[str] = Query(None, description="访问者UUID"),
    share_uuid: Optional[str] = Query(None, description="分享UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """店铺首页接口
    
    获取店铺首页数据，包括店铺信息、网站信息、产品列表和阿姨列表
    
    原始API路径: /micrositev3/storeHome
    """
    try:
        # 调用服务层获取店铺首页
        result = await MicrositeService.store_home_service(query_db, store_uuid, visitor_uuid, share_uuid)
        
        return ResponseUtil.success(
            msg="获取店铺首页成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取店铺首页数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"店铺不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询店铺首页失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取店铺首页业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取店铺首页异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
