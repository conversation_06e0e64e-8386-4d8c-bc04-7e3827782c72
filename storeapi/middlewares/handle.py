from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from middlewares.cors_middleware import add_cors_middleware
from middlewares.gzip_middleware import add_gzip_middleware
from middlewares.trace_middleware import add_trace_middleware
from middlewares.swagger_auth import swagger_auth_middleware


def handle_middleware(app: FastAPI):
    """
    全局中间件处理
    """
    # 加载跨域中间件
    add_cors_middleware(app)
    # 加载gzip压缩中间件
    add_gzip_middleware(app)
    # 加载trace中间件
    add_trace_middleware(app)

    # 添加 Swagger 认证中间件
    app.middleware("http")(swagger_auth_middleware)
