from datetime import datetime
from sqlalchemy import Column, String, Integer, DateTime, BigInteger, Text
from config.database import Base


class CCUserExtend(Base):
    """CC用户扩展信息表"""
    __tablename__ = 'ccuser_extend'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    ccuser_id = Column(String(64), nullable=True, comment='关联的ccuser用户ID')
    name = Column(String(255), nullable=True, comment='联系人姓名')
    contact_phone = Column(String(20), nullable=True, comment='联系电话')
    address = Column(String(255), nullable=True, comment='详细地址')
    address_desc = Column(String(255), nullable=True, comment='地址描述')
    
    # 地理位置信息
    lng = Column(String(20), nullable=True, comment='经度')
    lat = Column(String(20), nullable=True, comment='纬度')
    
    # 区域信息
    province = Column(String(50), nullable=True, comment='省份')
    city = Column(String(50), nullable=True, comment='城市')
    district = Column(String(50), nullable=True, comment='区县')
    
    # 状态字段
    status = Column(String(2), nullable=True, default='1', comment='状态')
    is_delete = Column(String(2), nullable=True, default='0', comment='是否删除')
    
    # 时间字段
    create_time = Column(DateTime, nullable=True, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=True, default=datetime.now, onupdate=datetime.now, comment='更新时间')
