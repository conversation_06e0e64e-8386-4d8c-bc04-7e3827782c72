from fastapi import APIRouter, Depends, Request, Query, Form, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any
from config.get_db import get_db
from module_admin.service.poster_service import PosterService
from module_admin.service.auth_adapter import AuthAdapter
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, ResourceNotFoundException, BusinessException, ValidationException

# 使用统一的API路由前缀格式
poster_controller = APIRouter(prefix='/api/v1/poster', dependencies=[Depends(AuthAdapter.get_current_user)])

@poster_controller.get('/getList', summary="获取海报列表")
async def get_poster_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    type: Optional[str] = Query(None, description="海报类型"),
    keyword: Optional[str] = Query(None, description="关键词"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取海报列表接口
    
    分页获取海报列表，支持按类型和关键词筛选
    
    原始API路径: /posterv3/getList
    """
    try:
        # 调用服务层获取海报列表
        result = await PosterService.get_poster_list_service(query_db, page, size, type, keyword)
        
        return ResponseUtil.success(
            msg="获取海报列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询海报列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取海报列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取海报列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@poster_controller.get('/getOne', summary="获取单个海报信息")
async def get_poster_one(
    request: Request,
    uuid: str = Query(..., description="海报UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取单个海报信息接口
    
    根据UUID获取海报的详细信息
    
    原始API路径: /posterv3/getOne
    """
    try:
        # 调用服务层获取单个海报信息
        result = await PosterService.get_poster_one_service(query_db, uuid)
        
        return ResponseUtil.success(
            msg="获取单个海报信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取单个海报信息数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"海报不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询单个海报信息失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取单个海报信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取单个海报信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@poster_controller.post('/save', summary="保存海报信息")
async def save_poster(
    request: Request,
    uuid: Optional[str] = Form(None, description="海报UUID"),
    title: str = Form(..., description="标题"),
    description: Optional[str] = Form("", description="描述"),
    type: str = Form(..., description="类型"),
    image_url: str = Form(..., description="图片URL"),
    share_image_url: Optional[str] = Form("", description="分享图片URL"),
    share_title: Optional[str] = Form("", description="分享标题"),
    share_description: Optional[str] = Form("", description="分享描述"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """保存海报信息接口
    
    保存海报信息，包括基本信息和分享信息
    
    原始API路径: /posterv3/save
    """
    try:
        # 获取当前用户信息
        current_user = await AuthAdapter.get_current_user(request)
        
        # 构建海报数据
        poster_data = {
            "uuid": uuid,
            "title": title,
            "description": description,
            "type": type,
            "image_url": image_url,
            "share_image_url": share_image_url,
            "share_title": share_title,
            "share_description": share_description,
            "creator_uuid": current_user.get("user_uuid")
        }
        
        # 调用服务层保存海报信息
        result = await PosterService.save_poster_service(query_db, poster_data)
        
        return ResponseUtil.success(
            msg="保存海报信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"保存海报信息数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"保存海报信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"保存海报信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@poster_controller.post('/delete', summary="删除海报")
async def delete_poster(
    request: Request,
    uuid: str = Form(..., description="海报UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """删除海报接口
    
    根据UUID删除海报
    
    原始API路径: /posterv3/delete
    """
    try:
        # 调用服务层删除海报
        result = await PosterService.delete_poster_service(query_db, uuid)
        
        return ResponseUtil.success(
            msg="删除海报成功",
            data={"success": result}
        )
    except ValidationException as e:
        logger.error(f"删除海报数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"海报不存在或删除失败: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"删除海报业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"删除海报异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@poster_controller.get('/getShareList', summary="获取海报分享列表")
async def get_poster_share_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    poster_uuid: Optional[str] = Query(None, description="海报UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取海报分享列表接口
    
    分页获取海报分享列表，支持按海报UUID筛选
    
    原始API路径: /posterv3/getShareList
    """
    try:
        # 调用服务层获取海报分享列表
        result = await PosterService.get_poster_share_list_service(query_db, page, size, poster_uuid)
        
        return ResponseUtil.success(
            msg="获取海报分享列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询海报分享列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取海报分享列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取海报分享列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@poster_controller.post('/saveShare', summary="保存海报分享信息")
async def save_poster_share(
    request: Request,
    poster_uuid: str = Form(..., description="海报UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """保存海报分享信息接口
    
    记录用户分享海报的信息
    
    原始API路径: /posterv3/saveShare
    """
    try:
        # 获取当前用户信息
        current_user = await AuthAdapter.get_current_user(request)
        
        # 构建分享数据
        share_data = {
            "poster_uuid": poster_uuid,
            "user_uuid": current_user.get("user_uuid")
        }
        
        # 调用服务层保存海报分享信息
        result = await PosterService.save_poster_share_service(query_db, share_data)
        
        return ResponseUtil.success(
            msg="保存海报分享信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"保存海报分享信息数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"保存海报分享信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"保存海报分享信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@poster_controller.get('/getVisitorData', summary="获取海报访问者数据")
async def get_poster_visitor_data(
    request: Request,
    poster_uuid: str = Query(..., description="海报UUID"),
    start_date: str = Query(..., description="开始日期"),
    end_date: str = Query(..., description="结束日期"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取海报访问者数据接口
    
    根据海报UUID和日期范围获取海报访问者数据
    
    原始API路径: /posterv3/getVisitorData
    """
    try:
        # 调用服务层获取海报访问者数据
        result = await PosterService.get_poster_visitor_data_service(query_db, poster_uuid, start_date, end_date)
        
        return ResponseUtil.success(
            msg="获取海报访问者数据成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取海报访问者数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询海报访问者数据失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取海报访问者数据业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取海报访问者数据异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@poster_controller.get('/view', summary="海报查看")
async def poster_view(
    request: Request,
    poster_uuid: str = Query(..., description="海报UUID"),
    visitor_uuid: Optional[str] = Query(None, description="访问者UUID"),
    share_uuid: Optional[str] = Query(None, description="分享UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """海报查看接口
    
    查看海报详情，并记录访问记录
    
    原始API路径: /posterv3/view
    """
    try:
        # 调用服务层海报查看
        result = await PosterService.poster_view_service(query_db, poster_uuid, visitor_uuid, share_uuid)
        
        return ResponseUtil.success(
            msg="海报查看成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"海报查看数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"海报不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"海报查看查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"海报查看业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"海报查看异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
