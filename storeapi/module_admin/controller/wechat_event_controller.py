"""
微信公众号事件处理控制器
"""
import hashlib
import xml.etree.ElementTree as ET
from fastapi import APIRouter, Request, Depends
from fastapi.responses import PlainTextResponse
from sqlalchemy.ext.asyncio import AsyncSession
from utils.log_util import logger
from utils.response_util import ResponseUtil
from config.get_db import get_db
from config.env import WechatConfig
from module_admin.service.wechat_event_service import WechatEventService
from utils.wechat_crypt import WechatCrypt


# 创建路由器
wechatEventController = APIRouter(prefix='/wechat/event', tags=['微信公众号事件'])


@wechatEventController.get('/callback', summary="微信公众号验证接口")
async def wechat_verify(
    signature: str,
    timestamp: str,
    nonce: str,
    echostr: str
):
    """
    微信公众号服务器验证接口
    
    Args:
        signature: 微信加密签名
        timestamp: 时间戳
        nonce: 随机数
        echostr: 随机字符串
        
    Returns:
        验证成功返回echostr，失败返回error
    """
    try:
        # 获取微信公众号的Token（需要在微信后台配置）
        token = getattr(WechatConfig, 'wechat_official_token', 'your_wechat_token')
        
        # 验证签名
        if verify_signature(signature, timestamp, nonce, token):
            logger.info("微信公众号验证成功")
            return PlainTextResponse(content=echostr)
        else:
            logger.error("微信公众号验证失败")
            return PlainTextResponse(content="error")
            
    except Exception as e:
        logger.error(f"微信公众号验证异常: {str(e)}")
        return PlainTextResponse(content="error")


@wechatEventController.post('/callback', summary="微信公众号事件接收接口")
async def wechat_event_handler(
    request: Request,
    signature: str,
    timestamp: str,
    nonce: str,
    msg_signature: str = None,
    db: AsyncSession = Depends(get_db)
):
    """
    微信公众号事件处理接口

    Args:
        request: HTTP请求对象
        signature: 微信加密签名
        timestamp: 时间戳
        nonce: 随机数
        msg_signature: 消息签名（加密模式）
        db: 数据库会话

    Returns:
        处理结果的XML响应
    """
    try:
        # 获取微信公众号配置
        token = getattr(WechatConfig, 'wechat_official_token', 'jingangai')
        encoding_aes_key = getattr(WechatConfig, 'wechat_official_encoding_aes_key', '')
        app_id = getattr(WechatConfig, 'wechat_official_app_id', '')

        # 创建加解密工具
        wechat_crypt = WechatCrypt(token, encoding_aes_key, app_id)

        # 获取POST数据
        body = await request.body()
        xml_data = body.decode('utf-8')

        logger.info(f"收到微信事件推送 - 数据长度: {len(xml_data)}")
        logger.info(f"收到微信事件推送 - 原始数据: {xml_data}")
        logger.info(f"微信回调参数 - signature: {signature}, timestamp: {timestamp}, nonce: {nonce}, msg_signature: {msg_signature}")

        # 检查是否是加密消息
        if '<Encrypt>' in xml_data:
            # 加密消息处理
            logger.info("处理加密消息")

            # 解析加密XML
            encrypt_data = wechat_crypt.parse_encrypted_xml(xml_data)
            encrypt_msg = encrypt_data.get('Encrypt', '')

            # 验证消息签名（使用URL参数中的msg_signature）
            if not msg_signature:
                logger.error("缺少msg_signature参数")
                return PlainTextResponse(content="error")

            if not wechat_crypt.verify_signature(msg_signature, timestamp, nonce, encrypt_msg):
                logger.error(f"加密消息签名验证失败: msg_signature={msg_signature}, timestamp={timestamp}, nonce={nonce}")
                return PlainTextResponse(content="error")

            # 解密消息
            decrypted_xml = wechat_crypt.decrypt_msg(encrypt_msg)
            if not decrypted_xml:
                logger.error("消息解密失败")
                return PlainTextResponse(content="error")

            logger.info(f"解密后的消息: {decrypted_xml}")
            xml_data = decrypted_xml
        else:
            # 明文消息处理
            logger.info("处理明文消息")
            if not wechat_crypt.verify_signature(signature, timestamp, nonce):
                logger.error("明文消息签名验证失败")
                return PlainTextResponse(content="error")

        # 解析XML数据
        root = ET.fromstring(xml_data)
        
        # 提取基本信息
        msg_data = {
            'ToUserName': root.find('ToUserName').text if root.find('ToUserName') is not None else '',
            'FromUserName': root.find('FromUserName').text if root.find('FromUserName') is not None else '',
            'CreateTime': root.find('CreateTime').text if root.find('CreateTime') is not None else '',
            'MsgType': root.find('MsgType').text if root.find('MsgType') is not None else '',
        }
        
        # 根据消息类型处理
        response_xml = ""
        
        if msg_data['MsgType'] == 'event':
            # 事件类型
            event = root.find('Event').text if root.find('Event') is not None else ''
            msg_data['Event'] = event

            logger.info(f"处理微信事件 - 类型: {event}, FromUserName: {msg_data.get('FromUserName', 'unknown')}")

            if event == 'subscribe':
                # 关注事件
                logger.info(f"处理关注事件 - 用户: {msg_data.get('FromUserName', 'unknown')}")
                response_xml = await WechatEventService.handle_subscribe_event(db, msg_data, root)
            elif event == 'SCAN':
                # 扫描带参数二维码事件（已关注用户）
                logger.info(f"处理扫描事件 - 用户: {msg_data.get('FromUserName', 'unknown')}")
                response_xml = await WechatEventService.handle_scan_event(db, msg_data, root)
            else:
                logger.info(f"未处理的事件类型: {event}")
                
        elif msg_data['MsgType'] == 'text':
            # 文本消息
            content = root.find('Content').text if root.find('Content') is not None else ''
            msg_data['Content'] = content
            response_xml = await WechatEventService.handle_text_message(db, msg_data)
        
        # 返回响应
        if response_xml:
            # 如果原消息是加密的，回复也需要加密
            if '<Encrypt>' in body.decode('utf-8'):
                encrypted_response = wechat_crypt.encrypt_msg(response_xml, timestamp, nonce)
                if encrypted_response:
                    return PlainTextResponse(content=encrypted_response, media_type="application/xml")
                else:
                    logger.error("加密回复消息失败")
                    return PlainTextResponse(content="success")
            else:
                return PlainTextResponse(content=response_xml, media_type="application/xml")
        else:
            return PlainTextResponse(content="success")
            
    except Exception as e:
        logger.error(f"处理微信事件异常: {str(e)}")
        return PlainTextResponse(content="error")


def verify_signature(signature: str, timestamp: str, nonce: str, token: str) -> bool:
    """
    验证微信签名
    
    Args:
        signature: 微信加密签名
        timestamp: 时间戳
        nonce: 随机数
        token: 微信Token
        
    Returns:
        验证是否成功
    """
    try:
        # 将token、timestamp、nonce三个参数进行字典序排序
        tmp_list = [token, timestamp, nonce]
        tmp_list.sort()
        
        # 将三个参数字符串拼接成一个字符串进行sha1加密
        tmp_str = ''.join(tmp_list)
        hash_obj = hashlib.sha1(tmp_str.encode('utf-8'))
        hash_str = hash_obj.hexdigest()
        
        # 开发者获得加密后的字符串可与signature对比，标识该请求来源于微信
        return hash_str == signature
        
    except Exception as e:
        logger.error(f"验证微信签名异常: {str(e)}")
        return False
