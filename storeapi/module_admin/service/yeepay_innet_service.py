"""
易宝支付入网服务
"""
import json
import httpx
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession

from config.env import YeepaySettings
from module_admin.service.payment_service import PaymentService
from exceptions.exception import BusinessException, ValidationException
from utils.response_util import ResponseUtil

logger = logging.getLogger(__name__)

# 易宝支付入网配置
class YeepayInnetConfig:
    """易宝支付入网配置"""
    
    # 继承基础配置
    yeepay_settings = YeepaySettings()
    
    # 入网接口地址
    INNET_ENTERPRISE_URL = 'https://api.kuaijie-pay.com/forward/innet/txn/v2/cus/innet/ent'  # 企业/个体户入网
    INNET_MICRO_URL = 'https://api.kuaijie-pay.com/forward/innet/txn/v2/cus/innet/micro'      # 小微/个人入网
    INNET_QUERY_URL = 'https://api.kuaijie-pay.com/forward/innet/txn/v2/cus/innet/query'      # 入网结果查询
    
    # 文件上传接口
    FILE_UPLOAD_URL = 'https://api.kuaijie-pay.com/forward/innet/txn/v2/file/upload'
    
    # 基础配置
    VERSION = yeepay_settings.YEEPAY_VERSION
    CUSTOMER_CODE = yeepay_settings.YEEPAY_CUSTOMER_CODE
    TIMEOUT = yeepay_settings.YEEPAY_TIMEOUT
    MERCHANT_PRIVATE_KEY = yeepay_settings.YEEPAY_MERCHANT_PRIVATE_KEY
    PLATFORM_PUBLIC_KEY = yeepay_settings.YEEPAY_PLATFORM_PUBLIC_KEY


class YeepayInnetService:
    """易宝支付入网服务类"""
    
    @classmethod
    async def enterprise_innet_service(
        cls,
        query_db: AsyncSession,
        innet_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """企业/个体户入网服务
        
        Args:
            query_db: 数据库会话
            innet_data: 入网数据
            
        Returns:
            入网结果
        """
        try:
            # 验证必填参数
            cls._validate_enterprise_innet_data(innet_data)
            
            # 构建入网请求参数
            request_data = cls._build_enterprise_innet_request(innet_data)
            
            # 调用易宝入网API
            result = await cls._call_yeepay_innet_api(
                YeepayInnetConfig.INNET_ENTERPRISE_URL,
                request_data
            )
            
            # 保存入网记录
            await cls._save_innet_record(query_db, innet_data, result, 'enterprise')
            
            return result
            
        except ValidationException as e:
            raise e
        except BusinessException as e:
            raise e
        except Exception as e:
            logger.error(f"企业/个体户入网异常: {str(e)}")
            raise BusinessException(message=f"入网失败: {str(e)}")
    
    @classmethod
    async def micro_innet_service(
        cls,
        query_db: AsyncSession,
        innet_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """小微/个人入网服务
        
        Args:
            query_db: 数据库会话
            innet_data: 入网数据
            
        Returns:
            入网结果
        """
        try:
            # 验证必填参数
            cls._validate_micro_innet_data(innet_data)
            
            # 构建入网请求参数
            request_data = cls._build_micro_innet_request(innet_data)
            
            # 调用易宝入网API
            result = await cls._call_yeepay_innet_api(
                YeepayInnetConfig.INNET_MICRO_URL,
                request_data
            )
            
            # 保存入网记录
            await cls._save_innet_record(query_db, innet_data, result, 'micro')
            
            return result
            
        except ValidationException as e:
            raise e
        except BusinessException as e:
            raise e
        except Exception as e:
            logger.error(f"小微/个人入网异常: {str(e)}")
            raise BusinessException(message=f"入网失败: {str(e)}")
    
    @classmethod
    async def query_innet_result_service(
        cls,
        query_db: AsyncSession,
        orig_txn_date: str,
        orig_cus_trace_no: str = None,
        orig_sys_trace_no: str = None
    ) -> Dict[str, Any]:
        """查询入网结果服务

        Args:
            query_db: 数据库会话
            orig_txn_date: 入网请求日期（格式：yyyyMMdd）
            orig_cus_trace_no: 入网请求的客户流水号（可选）
            orig_sys_trace_no: 入网请求的系统跟踪号（可选）

        Returns:
            查询结果
        """
        try:
            logger.info(f"=== YeepayInnetService 开始查询入网结果 ===")
            logger.info(f"查询参数:")
            logger.info(f"  - orig_txn_date: {orig_txn_date}")
            logger.info(f"  - orig_cus_trace_no: {orig_cus_trace_no}")
            logger.info(f"  - orig_sys_trace_no: {orig_sys_trace_no}")

            # 验证参数：origCusTraceNo和origSysTraceNo不能同时为空
            if not orig_cus_trace_no and not orig_sys_trace_no:
                logger.error(f"参数验证失败: 客户流水号和系统跟踪号不能同时为空")
                raise ValidationException(message="客户流水号和系统跟踪号不能同时为空")

            logger.info(f"参数验证通过")

            # 构建查询请求参数
            logger.info(f"构建易宝查询请求参数...")
            request_data = cls._build_query_request(orig_txn_date, orig_cus_trace_no, orig_sys_trace_no)
            logger.info(f"请求参数构建完成: {request_data}")

            # 调用易宝查询API
            logger.info(f"准备调用易宝查询API")
            logger.info(f"API地址: {YeepayInnetConfig.INNET_QUERY_URL}")

            result = await cls._call_yeepay_innet_api(
                YeepayInnetConfig.INNET_QUERY_URL,
                request_data
            )

            logger.info(f"易宝查询API调用完成")
            logger.info(f"API返回结果: {result}")
            logger.info(f"=== YeepayInnetService 查询入网结果完成 ===")

            return result

        except ValidationException as e:
            logger.error(f"YeepayInnetService 查询入网结果验证异常: {e.message}")
            raise e
        except Exception as e:
            logger.error(f"YeepayInnetService 查询入网结果系统异常: {str(e)}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            raise BusinessException(message=f"查询失败: {str(e)}")
    
    @classmethod
    async def upload_file_service(
        cls,
        file_content: bytes,
        file_name: str,
        file_type: str
    ) -> Dict[str, Any]:
        """文件上传服务
        
        Args:
            file_content: 文件内容
            file_name: 文件名
            file_type: 文件类型
            
        Returns:
            上传结果，包含文件ID
        """
        try:
            # 构建文件上传请求
            request_data = cls._build_file_upload_request(file_name, file_type)
            
            # 调用文件上传API
            result = await cls._call_yeepay_file_upload_api(request_data, file_content)
            
            return result
            
        except Exception as e:
            logger.error(f"文件上传异常: {str(e)}")
            raise BusinessException(message=f"文件上传失败: {str(e)}")
    
    @classmethod
    def _validate_enterprise_innet_data(cls, data: Dict[str, Any]) -> None:
        """验证企业/个体户入网数据"""
        required_fields = [
            'innetOwner',    # 入网主体信息
            'innetJuridical', # 法人信息
            'innetContact',   # 联系人信息
        ]
        
        for field in required_fields:
            if field not in data or not data[field]:
                raise ValidationException(message=f"缺少必填参数: {field}")
        
        # 验证入网主体信息
        owner = data['innetOwner']
        owner_required = ['ownerType', 'ownerName', 'ownerCertNo', 'ownerCertPic']
        for field in owner_required:
            if field not in owner or not owner[field]:
                raise ValidationException(message=f"入网主体信息缺少必填参数: {field}")
        
        # 验证法人信息
        juridical = data['innetJuridical']
        juridical_required = ['juridicalName', 'juridicalCertNo', 'juridicalCertPic', 'juridicalCertBackPic']
        for field in juridical_required:
            if field not in juridical or not juridical[field]:
                raise ValidationException(message=f"法人信息缺少必填参数: {field}")
    
    @classmethod
    def _validate_micro_innet_data(cls, data: Dict[str, Any]) -> None:
        """验证小微/个人入网数据"""
        required_fields = [
            'innetOwner',    # 入网主体信息
            'innetJuridical', # 法人信息
        ]
        
        for field in required_fields:
            if field not in data or not data[field]:
                raise ValidationException(message=f"缺少必填参数: {field}")
        
        # 验证入网主体信息
        owner = data['innetOwner']
        owner_required = ['ownerName', 'ownerShortName']
        for field in owner_required:
            if field not in owner or not owner[field]:
                raise ValidationException(message=f"入网主体信息缺少必填参数: {field}")
        
        # 验证法人信息
        juridical = data['innetJuridical']
        juridical_required = ['juridicalName', 'juridicalCertNo', 'juridicalCertPic', 'juridicalCertBackPic']
        for field in juridical_required:
            if field not in juridical or not juridical[field]:
                raise ValidationException(message=f"法人信息缺少必填参数: {field}")
    
    @classmethod
    def _build_enterprise_innet_request(cls, innet_data: Dict[str, Any]) -> Dict[str, Any]:
        """构建企业/个体户入网请求参数"""
        try:
            # 生成请求时间和流水号
            req_time = datetime.now().strftime("%Y%m%d%H%M%S")
            trace_no = f"INNET{req_time}{cls._generate_random_suffix()}"
            
            # 构建请求参数
            request_data = {
                "version": YeepayInnetConfig.VERSION,
                "cusReqTime": req_time,
                "cusTraceNo": trace_no,
                "cusCode": YeepayInnetConfig.CUSTOMER_CODE,
                **innet_data
            }
            
            return request_data
            
        except Exception as e:
            logger.error(f"构建企业入网请求参数异常: {str(e)}")
            raise BusinessException(message=f"构建请求参数失败: {str(e)}")
    
    @classmethod
    def _build_micro_innet_request(cls, innet_data: Dict[str, Any]) -> Dict[str, Any]:
        """构建小微/个人入网请求参数"""
        try:
            # 生成请求时间和流水号
            req_time = datetime.now().strftime("%Y%m%d%H%M%S")
            trace_no = f"MICRO{req_time}{cls._generate_random_suffix()}"
            
            # 构建请求参数
            request_data = {
                "version": YeepayInnetConfig.VERSION,
                "cusReqTime": req_time,
                "cusTraceNo": trace_no,
                "cusCode": YeepayInnetConfig.CUSTOMER_CODE,
                **innet_data
            }
            
            return request_data
            
        except Exception as e:
            logger.error(f"构建小微入网请求参数异常: {str(e)}")
            raise BusinessException(message=f"构建请求参数失败: {str(e)}")
    
    @classmethod
    def _build_query_request(
        cls,
        orig_txn_date: str,
        orig_cus_trace_no: str = None,
        orig_sys_trace_no: str = None
    ) -> Dict[str, Any]:
        """构建查询请求参数"""
        req_time = datetime.now().strftime("%Y%m%d%H%M%S")
        trace_no = f"QUERY{req_time}{cls._generate_random_suffix()}"

        # 构建公共参数体
        msg_public = {
            "version": YeepayInnetConfig.VERSION,
            "cusReqTime": req_time,
            "cusTraceNo": trace_no,
            "cusCode": YeepayInnetConfig.CUSTOMER_CODE
        }

        # 构建私有参数体
        msg_private = {
            "origTxnDate": orig_txn_date
        }

        # 添加客户流水号或系统跟踪号（至少一个不为空）
        if orig_cus_trace_no:
            msg_private["origCusTraceNo"] = orig_cus_trace_no
        if orig_sys_trace_no:
            msg_private["origSysTraceNo"] = orig_sys_trace_no

        return {
            "msgPublic": msg_public,
            "msgPrivate": msg_private
        }
    
    @classmethod
    def _build_file_upload_request(cls, file_name: str, file_type: str) -> Dict[str, Any]:
        """构建文件上传请求参数"""
        req_time = datetime.now().strftime("%Y%m%d%H%M%S")
        trace_no = f"UPLOAD{req_time}{cls._generate_random_suffix()}"
        
        return {
            "version": YeepayInnetConfig.VERSION,
            "cusReqTime": req_time,
            "cusTraceNo": trace_no,
            "cusCode": YeepayInnetConfig.CUSTOMER_CODE,
            "fileName": file_name,
            "fileType": file_type
        }
    
    @classmethod
    def _generate_random_suffix(cls) -> str:
        """生成随机后缀"""
        import random
        import string
        return ''.join(random.choices(string.digits, k=6))

    @classmethod
    async def _call_yeepay_innet_api(cls, api_url: str, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """调用易宝入网API"""
        try:
            logger.info(f"=== 开始调用易宝入网API ===")
            logger.info(f"API地址: {api_url}")
            logger.info(f"请求数据: {request_data}")

            # 构建完整请求体
            logger.info(f"步骤1: 构建请求体")
            request_body = {"body": request_data}
            logger.info(f"请求体构建完成: {request_body}")

            # 生成签名
            logger.info(f"步骤2: 生成请求签名")
            merchant_private_key = YeepayInnetConfig.MERCHANT_PRIVATE_KEY
            if not merchant_private_key:
                logger.error(f"易宝支付商户私钥未配置")
                raise BusinessException(message="易宝支付商户私钥未配置")

            logger.info(f"商户私钥已配置，长度: {len(merchant_private_key) if merchant_private_key else 0}")

            # 将body转换为JSON字符串进行签名
            body_json = json.dumps(request_body["body"], ensure_ascii=False, separators=(',', ':'))
            logger.info(f"签名内容: {body_json}")
            logger.info(f"签名内容长度: {len(body_json)}")

            # 生成签名（复用支付服务的签名方法）
            logger.info(f"开始生成RSA签名...")
            from module_admin.service.payment_service import PaymentService
            signature = PaymentService._generate_rsa_signature(body_json, merchant_private_key)
            logger.info(f"RSA签名生成成功: {signature[:50]}...（已截断）")

            # 添加签名到请求体
            request_body["sign"] = signature
            logger.info(f"签名已添加到请求体")

            # 发送HTTP请求
            logger.info(f"步骤3: 发送HTTP请求")
            logger.info(f"请求URL: {api_url}")
            logger.info(f"请求方法: POST")
            logger.info(f"请求头: Content-Type: application/json")
            logger.info(f"超时设置: {YeepayInnetConfig.TIMEOUT}秒")

            async with httpx.AsyncClient(timeout=YeepayInnetConfig.TIMEOUT) as client:
                logger.info(f"HTTP客户端创建成功，开始发送请求...")

                response = await client.post(
                    api_url,
                    json=request_body,
                    headers={"Content-Type": "application/json"}
                )

                logger.info(f"HTTP请求发送完成")
                logger.info(f"响应状态码: {response.status_code}")
                logger.info(f"响应头: {dict(response.headers)}")

                if response.status_code != 200:
                    logger.error(f"HTTP请求失败，状态码: {response.status_code}")
                    logger.error(f"响应内容: {response.text}")
                    raise BusinessException(message=f"入网API请求失败，HTTP状态码: {response.status_code}")

                # 解析响应
                logger.info(f"步骤4: 解析响应数据")
                response_text = response.text
                logger.info(f"原始响应内容: {response_text}")
                logger.info(f"响应内容长度: {len(response_text)}")

                try:
                    response_data = response.json()
                    logger.info(f"JSON解析成功")
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析失败: {str(e)}")
                    logger.error(f"响应内容: {response_text}")
                    raise BusinessException(message="入网API响应格式错误")

                logger.info(f"解析后的响应数据: {response_data}")

                # 验证响应签名（如果有配置公钥）
                logger.info(f"步骤5: 验证响应签名")
                response_sign = response_data.get("sign")
                response_body = response_data.get("body")

                logger.info(f"响应签名: {response_sign[:50] if response_sign else 'None'}...（已截断）")
                logger.info(f"响应体: {response_body}")

                if response_sign and response_body:
                    platform_public_key = YeepayInnetConfig.PLATFORM_PUBLIC_KEY
                    if platform_public_key and platform_public_key.strip():
                        logger.info(f"平台公钥已配置，开始验证签名...")
                        # 与支付接口保持一致，直接验证response_body
                        logger.info(f"签名验证内容: {response_body}")

                        is_valid = PaymentService._verify_rsa_signature(response_body, response_sign, platform_public_key)
                        if not is_valid:
                            logger.error("响应签名验证失败")
                            # 在生产环境中应该抛出异常，但在开发阶段可以跳过验证
                            logger.warning("⚠️ 开发环境跳过签名验证，生产环境请配置正确的平台公钥")
                            # raise BusinessException(message="入网API响应签名验证失败")
                        else:
                            logger.info("✅ 响应签名验证成功")
                    else:
                        logger.warning("⚠️ 平台公钥未配置，跳过签名验证")
                else:
                    logger.warning("⚠️ 响应中缺少签名或响应体，跳过签名验证")

                # 提取最终结果
                logger.info(f"步骤6: 提取最终结果")
                final_result = response_data.get("body", response_data)
                logger.info(f"最终返回结果: {final_result}")
                logger.info(f"=== 易宝入网API调用完成 ===")

                return final_result

        except BusinessException as e:
            logger.error(f"易宝入网API业务异常: {e.message}")
            raise e
        except Exception as e:
            logger.error(f"易宝入网API系统异常: {str(e)}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            raise BusinessException(message=f"入网API调用失败: {str(e)}")

    @classmethod
    async def _call_yeepay_file_upload_api(cls, request_data: Dict[str, Any], file_content: bytes) -> Dict[str, Any]:
        """调用易宝文件上传API"""
        try:
            # 构建multipart/form-data请求
            files = {
                'file': ('upload.jpg', file_content, 'image/jpeg')
            }

            # 构建表单数据
            form_data = {
                'body': json.dumps(request_data, ensure_ascii=False, separators=(',', ':'))
            }

            # 生成签名
            merchant_private_key = YeepayInnetConfig.MERCHANT_PRIVATE_KEY
            if not merchant_private_key:
                raise BusinessException(message="易宝支付商户私钥未配置")

            body_json = json.dumps(request_data, ensure_ascii=False, separators=(',', ':'))
            signature = PaymentService._generate_rsa_signature(body_json, merchant_private_key)
            form_data['sign'] = signature

            async with httpx.AsyncClient(timeout=YeepayInnetConfig.TIMEOUT) as client:
                response = await client.post(
                    YeepayInnetConfig.FILE_UPLOAD_URL,
                    data=form_data,
                    files=files
                )

                logger.info(f"文件上传API HTTP状态码: {response.status_code}")

                if response.status_code != 200:
                    logger.error(f"文件上传API HTTP请求失败，状态码: {response.status_code}")
                    raise BusinessException(message=f"文件上传API请求失败，HTTP状态码: {response.status_code}")

                response_data = response.json()
                logger.info(f"文件上传API响应: {response_data}")

                return response_data.get("body", response_data)

        except BusinessException:
            raise
        except Exception as e:
            logger.error(f"调用文件上传API异常: {str(e)}")
            raise BusinessException(message=f"文件上传API调用失败: {str(e)}")

    @classmethod
    async def _save_innet_record(
        cls,
        query_db: AsyncSession,
        innet_data: Dict[str, Any],
        result: Dict[str, Any],
        innet_type: str
    ) -> None:
        """保存入网记录"""
        try:
            # 这里可以根据需要保存入网记录到数据库
            # 暂时只记录日志
            logger.info(f"保存{innet_type}入网记录: {innet_data}")
            logger.info(f"入网结果: {result}")

            # TODO: 实现数据库保存逻辑
            # 可以创建一个入网记录表来保存相关信息

        except Exception as e:
            logger.error(f"保存入网记录异常: {str(e)}")
            # 不抛出异常，避免影响主流程
