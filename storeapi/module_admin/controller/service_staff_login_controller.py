"""
员工登录控制器
"""
from fastapi import APIRouter, Depends, Request, Form, Body, Query
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_admin.service.service_staff_login_service import ServiceStaffLoginService
from module_admin.service.service_staff_wechat_service import ServiceStaffWechatService
from module_admin.entity.do.service_staff import ServiceStaff
from module_admin.entity.vo.service_staff_login_vo import (
    ServiceStaffLoginResponse, ServiceStaffInfoModel, StaffResetPasswordRequest
)
from module_admin.entity.vo.service_staff_wechat_vo import (
    StaffWechatQrCodeRequest,
    StaffWechatQrCodeResponse,
    StaffWechatBindStatusResponse,
    StaffWechatBindRequest,
    StaffWechatUnbindRequest
)
from utils.response_util import ResponseUtil
from utils.log_util import logger
from utils.message_util import send_sms_verification_code


# 创建路由器
router = APIRouter(prefix="/api/v1/staff-login", tags=["服务人员登录"])


@router.post("/password", summary="密码登录", response_model=ServiceStaffLoginResponse)
async def password_login(
    request: Request,
    mobile: str = Form(...),
    password: str = Form(...),
    query_db: AsyncSession = Depends(get_db)
):
    """
    员工密码登录

    :param request: Request对象
    :param mobile: 手机号
    :param password: 密码
    :param query_db: 数据库会话
    :return: 登录结果
    """
    try:
        logger.info(f"员工密码登录请求，手机号: {mobile}")

        result = await ServiceStaffLoginService.password_login(
            request=request,
            query_db=query_db,
            mobile=mobile,
            password=password
        )

        logger.info(f"员工密码登录成功，手机号: {mobile}")
        return ResponseUtil.success(data=result, msg="登录成功")

    except Exception as e:
        logger.error(f"员工密码登录失败: {str(e)}")
        return ResponseUtil.business_error(msg=str(e))


@router.post("/send-sms", summary="发送短信验证码")
async def send_sms_code(
    request: Request,
    mobile: str = Form(...)
):
    """
    发送短信验证码

    :param request: Request对象
    :param mobile: 手机号
    :return: 发送结果
    """
    try:
        logger.info(f"员工发送短信验证码，手机号: {mobile}")

        # 发送短信验证码，使用原有的message_util函数
        result = await send_sms_verification_code(request, mobile, "")

        if result.get("success"):
            logger.info(f"员工短信验证码发送成功，手机号: {mobile}")
            return ResponseUtil.success(msg="验证码发送成功")
        else:
            logger.error(f"员工短信验证码发送失败，手机号: {mobile}")
            return ResponseUtil.failure(msg=result.get("message", "验证码发送失败"))

    except Exception as e:
        logger.error(f"员工发送短信验证码异常: {str(e)}")
        return ResponseUtil.error(msg=f"发送验证码失败: {str(e)}")


@router.post("/sms", summary="短信验证码登录", response_model=ServiceStaffLoginResponse)
async def sms_login(
    request: Request,
    mobile: str = Form(...),
    sms_code: str = Form(...),
    query_db: AsyncSession = Depends(get_db)
):
    """
    员工短信验证码登录

    :param request: Request对象
    :param mobile: 手机号
    :param sms_code: 短信验证码
    :param query_db: 数据库会话
    :return: 登录结果
    """
    try:
        logger.info(f"员工短信登录请求，手机号: {mobile}")

        result = await ServiceStaffLoginService.sms_login(
            request=request,
            query_db=query_db,
            mobile=mobile,
            sms_code=sms_code
        )

        logger.info(f"员工短信登录成功，手机号: {mobile}")
        return ResponseUtil.success(data=result, msg="登录成功")

    except Exception as e:
        logger.error(f"员工短信登录失败: {str(e)}")
        return ResponseUtil.business_error(msg=str(e))


@router.post("/wechat", summary="微信登录")
async def wechat_login(
    request: Request,
    wx_code: str = Form(...),
    query_db: AsyncSession = Depends(get_db)
):
    """
    员工微信登录

    :param request: Request对象
    :param wx_code: 微信授权码
    :param query_db: 数据库会话
    :return: 登录结果
    """
    try:
        logger.info(f"员工微信登录请求，授权码: {wx_code}")

        result = await ServiceStaffLoginService.wechat_login(
            request=request,
            query_db=query_db,
            wx_code=wx_code
        )
        
        if result.get('need_bind'):
            logger.info(f"员工微信需要绑定手机号，OpenID: {result.get('wx_openid')}")
            return ResponseUtil.success(data=result, msg="需要绑定手机号")
        else:
            logger.info(f"员工微信登录成功")
            return ResponseUtil.success(data=result, msg="登录成功")

    except Exception as e:
        logger.error(f"员工微信登录失败: {str(e)}")
        return ResponseUtil.business_error(msg=str(e))


@router.post("/phone-quick-auth", summary="手机号快速验证登录", response_model=ServiceStaffLoginResponse)
async def phone_quick_auth(
    request: Request,
    wx_code: str = Form(...),
    encrypted_data: str = Form(...),
    iv: str = Form(...),
    query_db: AsyncSession = Depends(get_db)
):
    """
    员工手机号快速验证登录

    :param request: Request对象
    :param wx_code: 微信授权码
    :param encrypted_data: 加密的手机号数据
    :param iv: 初始向量
    :param query_db: 数据库会话
    :return: 登录结果
    """
    try:
        logger.info(f"员工手机号快速验证登录请求，授权码: {wx_code}")

        result = await ServiceStaffLoginService.phone_quick_auth(
            request=request,
            query_db=query_db,
            wx_code=wx_code,
            encrypted_data=encrypted_data,
            iv=iv
        )

        logger.info(f"员工手机号快速验证登录成功")
        return ResponseUtil.success(data=result, msg="登录成功")

    except Exception as e:
        logger.error(f"员工手机号快速验证登录失败: {str(e)}")
        return ResponseUtil.business_error(msg=str(e))


@router.post("/wechat-bind", summary="微信绑定手机号", response_model=ServiceStaffLoginResponse)
async def wechat_bind(
    request: Request,
    wx_openid: str = Form(...),
    mobile: str = Form(...),
    sms_code: str = Form(...),
    query_db: AsyncSession = Depends(get_db)
):
    """
    员工微信绑定手机号

    :param request: Request对象
    :param wx_openid: 微信OpenID
    :param mobile: 手机号
    :param sms_code: 短信验证码
    :param query_db: 数据库会话
    :return: 绑定结果
    """
    try:
        logger.info(f"员工微信绑定请求，手机号: {mobile}, OpenID: {wx_openid}")

        result = await ServiceStaffLoginService.wechat_bind(
            request=request,
            query_db=query_db,
            wx_openid=wx_openid,
            mobile=mobile,
            sms_code=sms_code
        )

        logger.info(f"员工微信绑定成功，手机号: {mobile}")
        return ResponseUtil.success(data=result, msg="绑定成功")

    except Exception as e:
        logger.error(f"员工微信绑定失败: {str(e)}")
        return ResponseUtil.business_error(msg=str(e))


@router.get("/getinfo", summary="获取员工信息", response_model=ServiceStaffInfoModel)
async def get_staff_info(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
    current_staff=Depends(ServiceStaffLoginService.get_current_staff)
):
    """
    获取当前登录员工信息
    
    :param request: Request对象
    :param query_db: 数据库会话
    :param current_staff: 当前员工
    :return: 员工信息
    """
    try:
        logger.info(f"获取员工信息，员工ID: {current_staff.id}")
        
        # 获取员工所有公司信息
        from module_admin.dao.service_staff_dao import ServiceStaffDao
        staff_list = await ServiceStaffDao.get_staff_by_mobile(query_db, current_staff.mobile)
        
        # 构建员工信息
        staff_info = ServiceStaffLoginService._build_staff_info(current_staff, staff_list)
        
        result = {
            'staff_info': staff_info
        }
        
        logger.info(f"获取员工信息成功，员工ID: {current_staff.id}")
        return ResponseUtil.success(data=result, msg="获取成功")

    except Exception as e:
        logger.error(f"获取员工信息失败: {str(e)}")
        return ResponseUtil.business_error(msg=str(e))


@router.get("/get-store-contact", summary="获取门店联系方式")
async def get_store_contact(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
    current_staff=Depends(ServiceStaffLoginService.get_current_staff)
):
    """
    获取当前员工所在门店的联系方式

    :param request: Request对象
    :param query_db: 数据库会话
    :param current_staff: 当前员工
    :return: 门店联系方式信息
    """
    try:
        logger.info(f"获取门店联系方式，员工ID: {current_staff.id}, 门店UUID: {current_staff.store_uuid}")

        # 调用服务层获取门店联系方式
        from module_admin.service.store_contact_service import StoreContactService
        result = await StoreContactService.get_store_contact_service(query_db, current_staff.store_uuid)

        logger.info(f"获取门店联系方式成功，员工ID: {current_staff.id}")
        return ResponseUtil.success(data=result, msg="获取门店联系方式成功")

    except Exception as e:
        logger.error(f"获取门店联系方式失败: {str(e)}")
        return ResponseUtil.business_error(msg=f"获取门店联系方式失败: {str(e)}")


@router.post("/validate-company", summary="验证公司访问权限")
async def validate_company_access(
    request: Request,
    company_id: str = Form(...),
    query_db: AsyncSession = Depends(get_db),
    current_staff=Depends(ServiceStaffLoginService.get_current_staff)
):
    """
    验证员工是否可以访问指定公司（不修改数据库）

    :param request: Request对象
    :param company_id: 公司ID
    :param query_db: 数据库会话
    :param current_staff: 当前员工
    :return: 验证结果
    """
    try:
        logger.info(f"验证员工公司访问权限，员工ID: {current_staff.id}, 目标公司: {company_id}")

        result = await ServiceStaffLoginService.validate_company_access(
            request=request,
            query_db=query_db,
            current_staff=current_staff,
            company_id=company_id
        )

        logger.info(f"员工公司访问权限验证成功，员工ID: {current_staff.id}")
        return ResponseUtil.success(data=result, msg="验证成功")

    except Exception as e:
        logger.error(f"员工公司访问权限验证失败: {str(e)}")
        return ResponseUtil.business_error(msg=str(e))


@router.post("/set-default-company", summary="设置默认公司")
async def set_default_company(
    request: Request,
    company_id: str = Form(...),
    query_db: AsyncSession = Depends(get_db)
):
    """
    设置员工的默认公司（简化版本，避免重复查询）

    :param request: Request对象
    :param company_id: 公司ID
    :param query_db: 数据库会话
    :return: 设置结果
    """
    try:
        # 手动获取当前员工信息，避免依赖注入的复杂查询
        authorization = request.headers.get('authorization')
        if not authorization or not authorization.startswith('Bearer '):
            return ResponseUtil.business_error(msg="无效的访问令牌")

        token = authorization.split(' ')[1]

        # 解析token获取员工手机号
        import jwt
        from config.env import JwtConfig

        payload = jwt.decode(token, JwtConfig.jwt_secret_key, algorithms=[JwtConfig.jwt_algorithm])
        user_id = payload.get('user_id')
        mobile = payload.get('mobile')  # 直接从token获取手机号
        user_type = payload.get('user_type')

        if not user_id or not mobile or user_type != 'staff':
            return ResponseUtil.business_error(msg="无效的访问令牌")

        # 验证Redis中的token
        from config.env import AppConfig
        from config.enums import RedisInitKeyConfig

        if AppConfig.app_same_time_login:
            session_id = payload.get('session_id')
            redis_key = f'{RedisInitKeyConfig.ACCESS_TOKEN.key}:{session_id}'
        else:
            redis_key = f'{RedisInitKeyConfig.ACCESS_TOKEN.key}:{user_id}'

        redis_token = await request.app.state.redis.get(redis_key)
        if not redis_token:
            return ResponseUtil.business_error(msg="访问令牌已过期或无效")

        # 处理Redis返回的数据类型
        redis_token_str = redis_token.decode() if isinstance(redis_token, bytes) else str(redis_token)
        if redis_token_str != token:
            return ResponseUtil.business_error(msg="访问令牌已过期或无效")

        logger.info(f"设置员工默认公司，员工ID: {user_id}, 手机号: {mobile}, 目标公司: {company_id}")

        # 直接调用DAO层设置默认公司（完全避免数据库查询员工信息）
        from module_admin.dao.service_staff_dao import ServiceStaffDao
        success = await ServiceStaffDao.set_default_company(query_db, mobile, company_id)

        if success:
            logger.info(f"员工默认公司设置成功，员工ID: {user_id}")
            return ResponseUtil.success(data={
                'message': '默认公司设置成功',
                'default_company_id': company_id
            }, msg="默认公司设置成功")
        else:
            logger.error(f"员工默认公司设置失败，员工ID: {user_id}")
            return ResponseUtil.business_error(msg="设置默认公司失败")

    except jwt.ExpiredSignatureError:
        return ResponseUtil.business_error(msg="访问令牌已过期")
    except (jwt.InvalidTokenError, jwt.DecodeError, jwt.InvalidSignatureError):
        return ResponseUtil.business_error(msg="无效的访问令牌")
    except Exception as e:
        logger.error(f"员工默认公司设置系统异常: {str(e)}")
        return ResponseUtil.error(msg="系统异常，请稍后重试")


@router.post("/logout", summary="登出")
async def logout(
    request: Request,
    current_staff=Depends(ServiceStaffLoginService.get_current_staff)
):
    """
    员工登出
    
    :param request: Request对象
    :param current_staff: 当前员工
    :return: 登出结果
    """
    try:
        logger.info(f"员工登出，员工ID: {current_staff.id}")
        
        # 获取token
        authorization = request.headers.get('authorization')
        if not authorization or not authorization.startswith('Bearer '):
            return ResponseUtil.business_error(msg="无效的访问令牌")

        token = authorization.split(' ')[1]

        result = await ServiceStaffLoginService.logout(
            request=request,
            token=token,
            current_staff=current_staff
        )

        logger.info(f"员工登出成功，员工ID: {current_staff.id}")
        return ResponseUtil.success(data=result, msg="登出成功")

    except Exception as e:
        logger.error(f"员工登出失败: {str(e)}")
        return ResponseUtil.business_error(msg=str(e))


@router.post("/reset-password", summary="员工重置密码")
async def reset_password(
    request: Request,
    mobile: str = Form(..., description="手机号"),
    verify_code: str = Form(..., description="短信验证码"),
    new_password: str = Form(..., description="新密码"),
    confirm_password: str = Form(..., description="确认密码"),
    query_db: AsyncSession = Depends(get_db)
):
    """
    员工重置密码接口

    通过手机号和短信验证码重置员工密码，无需token认证

    :param request: Request对象
    :param mobile: 手机号
    :param verify_code: 短信验证码
    :param new_password: 新密码
    :param confirm_password: 确认密码
    :param query_db: 数据库会话
    :return: 重置结果
    """
    try:
        logger.info(f"收到员工重置密码请求，手机号: {mobile}")

        # 验证确认密码
        if new_password != confirm_password:
            logger.warning("两次输入的密码不一致")
            return ResponseUtil.failure(msg="两次输入的密码不一致")

        # 调用服务层重置密码
        result = await ServiceStaffLoginService.reset_password_service(
            request=request,
            query_db=query_db,
            mobile=mobile,
            verify_code=verify_code,
            new_password=new_password
        )

        if result:
            logger.info(f"员工密码重置成功，手机号: {mobile}")
            return ResponseUtil.success(msg="密码重置成功")
        else:
            logger.warning(f"员工密码重置失败，手机号: {mobile}")
            return ResponseUtil.failure(msg="密码重置失败")

    except Exception as e:
        logger.error(f"员工重置密码异常: {str(e)}")
        return ResponseUtil.error(msg=f"重置密码失败: {str(e)}")


@router.post("/check-account", summary="检查员工账号")
async def check_staff_account(
    request: Request,
    mobile: str = Form(..., description="手机号"),
    query_db: AsyncSession = Depends(get_db)
):
    """
    检查手机号下是否有员工账号

    :param request: Request对象
    :param mobile: 手机号
    :param query_db: 数据库会话
    :return: 检查结果
    """
    try:
        logger.info(f"检查员工账号，手机号: {mobile}")

        # 调用服务层检查员工账号
        result = await ServiceStaffLoginService.check_staff_account(
            query_db=query_db,
            mobile=mobile
        )

        logger.info(f"检查员工账号完成，手机号: {mobile}, 结果: {result}")
        return ResponseUtil.success(data=result, msg="检查完成")

    except Exception as e:
        logger.error(f"检查员工账号异常: {str(e)}")
        return ResponseUtil.error(msg=f"检查失败: {str(e)}")


@router.post("/switch-account", summary="切换到员工账号")
async def switch_to_staff_account(
    request: Request,
    mobile: str = Form(..., description="手机号"),
    query_db: AsyncSession = Depends(get_db)
):
    """
    切换到员工账号（获取默认员工账号token）

    :param request: Request对象
    :param mobile: 手机号
    :param query_db: 数据库会话
    :return: 切换结果
    """
    try:
        logger.info(f"切换到员工账号，手机号: {mobile}")

        # 调用服务层切换到员工账号
        result = await ServiceStaffLoginService.switch_to_staff_account(
            request=request,
            query_db=query_db,
            mobile=mobile
        )

        logger.info(f"切换到员工账号成功，手机号: {mobile}")
        return ResponseUtil.success(data=result, msg="切换成功")

    except Exception as e:
        logger.error(f"切换到员工账号失败: {str(e)}")
        return ResponseUtil.business_error(msg=str(e))


@router.post("/switch-to-store", summary="切换到管理端")
async def switch_to_store_account(
    request: Request,
    mobile: str = Form(..., description="手机号"),
    query_db: AsyncSession = Depends(get_db)
):
    """
    切换到管理端（获取管理端token）

    :param request: Request对象
    :param mobile: 手机号
    :param query_db: 数据库会话
    :return: 切换结果
    """
    try:
        logger.info(f"切换到管理端，手机号: {mobile}")

        # 调用服务层切换到管理端
        result = await ServiceStaffLoginService.switch_to_store_account(
            request=request,
            query_db=query_db,
            mobile=mobile
        )

        logger.info(f"切换到管理端成功，手机号: {mobile}")
        return ResponseUtil.success(data=result, msg="切换成功")

    except Exception as e:
        logger.error(f"切换到管理端失败: {str(e)}")
        return ResponseUtil.business_error(msg=str(e))


@router.get("/company-products", summary="获取员工公司产品列表")
async def get_staff_company_products(
    request: Request,
    company_id: str = Query(None, description="公司ID，不传则使用当前员工的默认公司"),
    query_db: AsyncSession = Depends(get_db),
    current_staff=Depends(ServiceStaffLoginService.get_current_staff)
):
    """
    获取员工公司产品列表接口（员工端专用）

    根据员工当前选择的公司或默认公司，获取该公司的所有产品列表

    :param request: Request对象
    :param company_id: 公司ID（可选，不传则使用员工的默认公司）
    :param query_db: 数据库会话
    :param current_staff: 当前员工信息
    :return: 公司产品列表
    """
    try:
        # 确定要查询的公司ID
        target_company_id = company_id or current_staff.company_id

        if not target_company_id:
            logger.warning(f"员工 {current_staff.mobile} 没有关联的公司信息")
            return ResponseUtil.business_error(msg="员工没有关联的公司信息")

        logger.info(f"员工 {current_staff.mobile} 获取公司 {target_company_id} 的产品列表")

        # 验证员工是否有权限访问该公司的产品
        # 查询员工是否属于该公司
        from sqlalchemy import text
        staff_company_query = text("""
            SELECT company_id, store_name
            FROM service_staff
            WHERE mobile = :mobile AND company_id = :company_id AND status = '1' AND is_delete = '0'
            LIMIT 1
        """)
        staff_company_result = await query_db.execute(staff_company_query, {
            "mobile": current_staff.mobile,
            "company_id": target_company_id
        })
        staff_company_row = staff_company_result.fetchone()

        if not staff_company_row:
            logger.warning(f"员工 {current_staff.mobile} 无权访问公司 {target_company_id} 的产品")
            return ResponseUtil.business_error(msg="无权访问该公司的产品信息")

        # 查询公司产品列表
        products_query = text("""
            SELECT
                p.id,
                p.product_name,
                p.service_skill_name,
                p.service_skill_main_name,
                p.img_id,
                p.product_status,
                p.details,
                p.uuid as product_uuid,
                -- 获取产品价格信息（从product_sku表）
                ps.now_price,
                ps.type_price_unit,
                ps.vip_price
            FROM product p
            LEFT JOIN product_sku ps ON p.id = ps.productid
            WHERE p.company_uuid = :company_id
                AND p.is_delete = 0
            ORDER BY p.create_time DESC
        """)

        products_result = await query_db.execute(products_query, {"company_id": target_company_id})
        products_rows = products_result.fetchall()

        # 处理产品数据
        products_list = []
        for row in products_rows:
            product_data = {
                "id": row.id,
                "product_name": row.product_name,
                "name": row.product_name,  # 兼容前端字段
                "service_skill_name": row.service_skill_name,
                "service_skill_main_name": row.service_skill_main_name,
                "img_id": row.img_id,
                "product_status": row.product_status,
                "details": row.details,
                "uuid": row.product_uuid,
                "sku_info": {
                    "now_price": str(row.now_price) if row.now_price else "0",
                    "type_price_unit": row.type_price_unit or "/次",
                    "vip_price": str(row.vip_price) if row.vip_price else "0"
                } if row.now_price else None
            }

            # 前端不再显示图片，移除图片处理逻辑以提升性能

            products_list.append(product_data)

        # 按服务分类分组
        categories = {}
        for product in products_list:
            category_name = product["service_skill_name"] or product["service_skill_main_name"] or "其他服务"
            if category_name not in categories:
                categories[category_name] = {
                    "name": category_name,
                    "products": []
                }
            categories[category_name]["products"].append(product)

        # 构建返回数据
        result = {
            "categories": list(categories.values()),
            "total_products": len(products_list),
            "total_categories": len(categories),
            "company_info": {
                "company_id": target_company_id,
                "store_name": staff_company_row.store_name
            }
        }

        logger.info(f"员工 {current_staff.mobile} 获取公司产品列表成功，共 {len(products_list)} 个产品")
        return ResponseUtil.success(data=result, msg="获取公司产品列表成功")

    except Exception as e:
        logger.error(f"获取员工公司产品列表失败: {str(e)}")
        return ResponseUtil.business_error(msg=f"获取产品列表失败: {str(e)}")


# ==================== 微信公众号相关接口 ====================

@router.post('/wechat-official/generate-qr-code', summary="生成员工微信公众号带参数二维码")
async def generate_staff_wechat_official_qr_code(
    request: StaffWechatQrCodeRequest,
    current_staff = Depends(ServiceStaffLoginService.get_current_staff)
):
    """
    生成员工微信公众号带参数二维码

    Args:
        request: 生成二维码请求数据
        current_staff: 当前员工

    Returns:
        二维码URL和相关信息
    """
    try:
        logger.info(f"员工 {current_staff.uuid} 请求生成微信公众号二维码")

        # 验证员工权限（只能为自己生成二维码）
        if request.staff_id != current_staff.uuid:
            logger.warning(f"员工 {current_staff.uuid} 尝试为其他员工 {request.staff_id} 生成二维码")
            return ResponseUtil.failure("只能为自己生成二维码")

        # 调用服务层生成二维码
        result = await ServiceStaffWechatService.generate_staff_qr_code(request)

        if result:
            logger.info(f"生成员工微信公众号二维码成功")
            return ResponseUtil.success(
                data=result.model_dump(),
                msg="生成二维码成功"
            )
        else:
            logger.error("生成员工微信公众号二维码失败")
            return ResponseUtil.failure("生成二维码失败，请稍后重试")

    except Exception as e:
        logger.error(f"生成员工微信公众号二维码异常: {str(e)}")
        return ResponseUtil.error(f"生成二维码失败: {str(e)}")


@router.get('/wechat-official/bind-status', summary="检查员工微信公众号绑定状态")
async def check_staff_wechat_official_bind_status(
    current_staff = Depends(ServiceStaffLoginService.get_current_staff),
    query_db: AsyncSession = Depends(get_db)
):
    """
    检查员工微信公众号绑定状态

    Args:
        current_staff: 当前员工
        query_db: 数据库会话

    Returns:
        绑定状态信息
    """
    try:
        logger.info(f"员工 {current_staff.uuid} 查询微信公众号绑定状态")

        # 调用服务层检查绑定状态
        result = await ServiceStaffWechatService.check_staff_bind_status(
            query_db,
            current_staff.uuid
        )

        return ResponseUtil.success(
            data=result.model_dump(),
            msg="查询绑定状态成功"
        )

    except Exception as e:
        logger.error(f"查询员工微信公众号绑定状态异常: {str(e)}")
        return ResponseUtil.error(f"查询绑定状态失败: {str(e)}")


@router.post('/wechat-official/bind', summary='员工微信公众号绑定', description='绑定微信公众号到员工账号')
async def bind_staff_wechat_official(
    bind_data: StaffWechatBindRequest = Body(..., description="微信绑定数据"),
    query_db: AsyncSession = Depends(get_db)
):
    """员工微信公众号绑定接口

    将微信公众号绑定到现有的员工手机号账号

    Args:
        bind_data: 微信绑定数据
        query_db: 数据库会话

    Returns:
        绑定结果
    """
    try:
        logger.info(f"收到员工微信公众号绑定请求，手机号: {bind_data.mobile}")

        # 调用服务层微信绑定
        result = await ServiceStaffWechatService.bind_staff_wechat(
            db=query_db,
            bind_data=bind_data
        )

        if result:
            return ResponseUtil.success(msg="微信公众号绑定成功")
        else:
            return ResponseUtil.failure(msg="微信公众号绑定失败")

    except Exception as e:
        logger.error(f"员工微信公众号绑定异常: {str(e)}")
        return ResponseUtil.error(msg=f"微信公众号绑定失败: {str(e)}")


@router.post('/wechat-official/unbind', summary='员工微信公众号解绑', description='解绑员工微信公众号')
async def unbind_staff_wechat_official(
    unbind_data: StaffWechatUnbindRequest = Body(..., description="微信解绑数据"),
    current_staff = Depends(ServiceStaffLoginService.get_current_staff),
    query_db: AsyncSession = Depends(get_db)
):
    """员工微信公众号解绑接口

    解绑员工的微信公众号

    Args:
        unbind_data: 微信解绑数据
        current_staff: 当前员工
        query_db: 数据库会话

    Returns:
        解绑结果
    """
    try:
        logger.info(f"收到员工微信公众号解绑请求，员工ID: {unbind_data.staff_id}")

        # 验证员工权限（只能解绑自己的微信）
        if unbind_data.staff_id != current_staff.uuid:
            logger.warning(f"员工 {current_staff.uuid} 尝试解绑其他员工 {unbind_data.staff_id} 的微信")
            return ResponseUtil.failure("只能解绑自己的微信")

        # TODO: 验证短信验证码
        # if not await verify_sms_code(current_staff.mobile, unbind_data.verify_code):
        #     return ResponseUtil.failure("验证码错误或已过期")

        # 调用服务层微信解绑
        result = await ServiceStaffWechatService.unbind_staff_wechat(
            db=query_db,
            staff_id=unbind_data.staff_id
        )

        if result:
            return ResponseUtil.success(msg="微信公众号解绑成功")
        else:
            return ResponseUtil.failure(msg="微信公众号解绑失败")

    except Exception as e:
        logger.error(f"员工微信公众号解绑异常: {str(e)}")
        return ResponseUtil.error(msg=f"微信公众号解绑失败: {str(e)}")


@router.post("/wechat-official/test-push", summary="测试员工微信公众号推送")
async def test_staff_wechat_official_push(
    query_db: AsyncSession = Depends(get_db),
    current_staff: ServiceStaff = Depends(ServiceStaffLoginService.get_current_staff)
):
    """
    测试员工微信公众号推送

    Args:
        query_db: 数据库会话
        current_staff: 当前员工信息

    Returns:
        推送测试结果
    """
    try:
        logger.info(f"员工 {current_staff.uuid} 请求测试微信公众号推送")

        # 调用服务层测试推送
        result = await ServiceStaffWechatService.test_staff_push(
            query_db,
            current_staff.uuid
        )

        if result.get('success'):
            logger.info(f"员工 {current_staff.uuid} 测试推送发送成功")
            return ResponseUtil.success(
                data={
                    "message_id": result.get('message_id'),
                    "send_time": result.get('send_time')
                },
                msg="测试推送发送成功"
            )
        else:
            error_msg = result.get('message', '推送发送失败')
            logger.error(f"员工 {current_staff.uuid} 测试推送发送失败: {error_msg}")
            return ResponseUtil.failure(msg=error_msg)

    except Exception as e:
        logger.error(f"测试员工微信公众号推送异常: {str(e)}")
        return ResponseUtil.error(msg="测试推送失败")
