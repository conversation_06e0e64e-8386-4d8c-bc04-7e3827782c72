from fastapi import APIRouter, Depends, Request, Query, Form, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any, List
from config.get_db import get_db
from module_admin.service.user_service import UserService
from module_admin.service.auth_adapter import AuthAdapter
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, ResourceNotFoundException, BusinessException, ValidationException

# 使用统一的API路由前缀格式
user_controller = APIRouter(prefix='/api/v1/user', dependencies=[Depends(AuthAdapter.get_current_user)])

@user_controller.get('/getUserInfo', summary="获取用户信息")
async def get_user_info(
    request: Request,
    user_uuid: Optional[str] = Query(None, description="用户UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取用户信息接口

    根据用户UUID获取用户详细信息，如果不传入用户UUID则获取当前登录用户信息

    原始API路径: /userv3/getUserInfo
    """
    try:
        # 获取当前用户信息
        current_user = await AuthAdapter.get_current_user(request)

        # 如果未指定用户UUID，则使用当前用户的UUID
        if not user_uuid:
            user_uuid = current_user.get("user_uuid")

        # 调用服务层获取用户信息
        result = await UserService.get_user_info_service(query_db, user_uuid)

        return ResponseUtil.success(
            msg="获取用户信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取用户信息数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"用户不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询用户信息失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取用户信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取用户信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@user_controller.post('/updateUserInfo', summary="更新用户信息")
async def update_user_info(
    request: Request,
    user_name: str = Form(..., description="用户名"),
    mobile: str = Form(..., description="手机号"),
    avatar: Optional[str] = Form("", description="头像"),
    email: Optional[str] = Form("", description="邮箱"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """更新用户信息接口

    更新当前登录用户的基本信息

    原始API路径: /userv3/updateUserInfo
    """
    try:
        # 获取当前用户信息
        current_user = await AuthAdapter.get_current_user(request)

        # 构建用户数据
        user_data = {
            "user_uuid": current_user.get("user_uuid"),
            "user_name": user_name,
            "mobile": mobile,
            "avatar": avatar,
            "email": email
        }

        # 调用服务层更新用户信息
        result = await UserService.update_user_info_service(query_db, user_data)

        return ResponseUtil.success(
            msg="更新用户信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"更新用户信息数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"用户不存在或更新失败: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"更新用户信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"更新用户信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@user_controller.post('/changePassword', summary="修改密码")
async def change_password(
    request: Request,
    old_password: str = Form(..., description="旧密码"),
    new_password: str = Form(..., description="新密码"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """修改密码接口

    修改当前登录用户的密码

    原始API路径: /userv3/changePassword
    """
    try:
        # 获取当前用户信息
        current_user = await AuthAdapter.get_current_user(request)

        # 调用服务层修改密码
        result = await UserService.change_password_service(query_db, current_user.get("user_uuid"), old_password, new_password)

        return ResponseUtil.success(
            msg="修改密码成功",
            data={"success": result}
        )
    except ValidationException as e:
        logger.error(f"修改密码数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"用户不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"修改密码业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"修改密码异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@user_controller.get('/getUserList', summary="获取用户列表")
async def get_user_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    keyword: Optional[str] = Query(None, description="关键词"),
    store_uuid: Optional[str] = Query(None, description="门店UUID"),
    role_id: Optional[int] = Query(None, description="角色ID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取用户列表接口

    分页获取用户列表，支持按关键词、门店UUID和角色ID筛选

    原始API路径: /userv3/getUserList
    """
    try:
        # 调用服务层获取用户列表
        result = await UserService.get_user_list_service(query_db, page, size, keyword, store_uuid, role_id)

        return ResponseUtil.success(
            msg="获取用户列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询用户列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取用户列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取用户列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@user_controller.get('/getRoleList', summary="获取角色列表")
async def get_role_list(
    request: Request,
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取角色列表接口

    获取所有角色列表

    原始API路径: /userv3/getRoleList
    """
    try:
        # 调用服务层获取角色列表
        result = await UserService.get_role_list_service(query_db)

        return ResponseUtil.success(
            msg="获取角色列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询角色列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取角色列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取角色列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@user_controller.post('/saveUser', summary="保存用户信息")
async def save_user(
    request: Request,
    user_uuid: Optional[str] = Form(None, description="用户UUID"),
    user_name: str = Form(..., description="用户名"),
    mobile: str = Form(..., description="手机号"),
    avatar: Optional[str] = Form("", description="头像"),
    email: Optional[str] = Form("", description="邮箱"),
    store_uuid: Optional[str] = Form(None, description="门店UUID"),
    role_ids: List[int] = Form(..., description="角色ID列表"),
    password: Optional[str] = Form(None, description="密码"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """保存用户信息接口

    保存用户信息，包括基本信息和角色信息

    原始API路径: /userv3/saveUser
    """
    try:
        # 构建用户数据
        user_data = {
            "user_uuid": user_uuid,
            "user_name": user_name,
            "mobile": mobile,
            "avatar": avatar,
            "email": email,
            "store_uuid": store_uuid,
            "role_ids": role_ids,
            "password": password
        }

        # 调用服务层保存用户信息
        result = await UserService.save_user_service(query_db, user_data)

        return ResponseUtil.success(
            msg="保存用户信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"保存用户信息数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"保存用户信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"保存用户信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@user_controller.post('/deleteUser', summary="删除用户")
async def delete_user(
    request: Request,
    user_uuid: str = Form(..., description="用户UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """删除用户接口

    根据用户UUID删除用户

    原始API路径: /userv3/deleteUser
    """
    try:
        # 调用服务层删除用户
        result = await UserService.delete_user_service(query_db, user_uuid)

        return ResponseUtil.success(
            msg="删除用户成功",
            data={"success": result}
        )
    except ValidationException as e:
        logger.error(f"删除用户数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"用户不存在或删除失败: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"删除用户业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"删除用户异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@user_controller.post('/resetPassword', summary="重置密码")
async def reset_password(
    request: Request,
    user_uuid: str = Form(..., description="用户UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """重置密码接口

    根据用户UUID重置用户密码为默认密码

    原始API路径: /userv3/resetPassword
    """
    try:
        # 调用服务层重置密码
        result = await UserService.reset_password_service(query_db, user_uuid)

        return ResponseUtil.success(
            msg="重置密码成功",
            data={"success": result}
        )
    except ValidationException as e:
        logger.error(f"重置密码数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"用户不存在或重置密码失败: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"重置密码业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"重置密码异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@user_controller.post('/ccUserDefaultAddress', summary="获取用户默认地址")
async def cc_user_default_address(
    request: Request,
    user_id: Optional[str] = Form(None, description="用户ID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取用户默认地址接口

    根据用户ID获取用户默认地址信息

    原始API路径: /user/ccUserDefaultAddress
    """
    try:
        # 获取当前用户信息
        current_user = await AuthAdapter.get_current_user(request)

        # 如果未指定用户ID，则使用当前用户的ID
        if not user_id:
            user_id = current_user.get("id")

        # 调用服务层获取用户默认地址
        result = await UserService.get_user_default_address_service(query_db, user_id)

        return ResponseUtil.success(
            msg="获取用户默认地址成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取用户默认地址数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"用户不存在或无默认地址: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询用户默认地址失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取用户默认地址业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取用户默认地址异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@user_controller.post('/searchCcUserList', summary="搜索CC用户列表")
async def search_cc_user_list(
    request: Request,
    keyword: Optional[str] = Form(None, description="关键词"),
    page: int = Form(1, description="页码"),
    size: int = Form(20, description="每页数量"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """搜索CC用户列表接口

    根据关键词搜索CC用户列表

    原始API路径: /user/searchCcUserList
    """
    try:
        # 调用服务层搜索CC用户列表
        result = await UserService.search_cc_user_list_service(query_db, keyword, page, size)

        return ResponseUtil.success(
            msg="搜索CC用户列表成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"搜索CC用户列表数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"搜索CC用户列表查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"搜索CC用户列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"搜索CC用户列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
