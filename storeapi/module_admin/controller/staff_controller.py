"""
服务员工管理控制器
"""
import logging
from typing import Optional, Dict, Any, List

from fastapi import APIRouter, Depends, Query, Body
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from module_admin.entity.vo.staff_skill_vo import StaffSkillResponseModel, StaffSkillQueryModel
from module_admin.service.staff_skill_service import StaffSkillService
from module_admin.service.internal_user_login_service import InternalUserLoginService
from config.get_db import get_db
from utils.response_util import ResponseUtil
from utils.log_util import logger

logger = logging.getLogger(__name__)
staff_controller = APIRouter(prefix='/api/v1/staff', dependencies=[Depends(InternalUserLoginService.get_current_user)])


# 员工状态更新请求模型
class StaffStatusUpdateRequest(BaseModel):
    staff_id: str
    status: str  # 员工状态：0-冻结，1-正常，2-下架，3-离职，4-拉黑


@staff_controller.get(
    "/skills",
    summary="获取服务员工技能列表",
    response_model=StaffSkillResponseModel
)
async def get_staff_skills(
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """获取服务员工技能列表

    获取当前用户公司购买的版本对应的服务技能列表

    根据当前登录用户的公司ID，自动查询该公司购买的所有版本，
    然后返回这些版本关联的技能列表

    返回的技能包含层级结构：
    - level=1: 一级技能分类
    - level=2: 二级技能分类
    """
    try:
        # 获取当前用户的公司ID
        company_id = current_user.user.company_id
        if not company_id:
            logger.warning("当前用户没有关联的公司信息")
            return ResponseUtil.failure(msg="用户没有关联的公司信息")

        logger.info(f"获取公司 {company_id} 的技能列表")

        # 获取技能列表
        skills = await StaffSkillService.get_staff_skills_by_company(db, company_id)

        return ResponseUtil.success(
            data=skills,
            msg=f"获取技能列表成功，共{len(skills)}项技能"
        )
    except Exception as e:
        logger.error(f"获取服务员工技能列表失败: {str(e)}")
        return ResponseUtil.failure(msg="获取技能列表失败")


@staff_controller.get('/getStaffDetail', summary="获取服务人员详情")
async def get_staff_detail(
    staff_id: str = Query(..., description="员工ID或UUID"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """获取服务人员详情接口

    根据员工ID或UUID获取完整的服务人员信息，包括基本信息和扩展信息

    :param staff_id: 员工ID或UUID
    :return: 服务人员详情
    """
    try:
        logger.info(f"获取服务人员详情，ID: {staff_id}")

        # 调用服务层获取员工详情
        from module_admin.service.staff_service import StaffService
        result = await StaffService.get_staff_detail_service(db, staff_id)

        return ResponseUtil.success(
            data=result,
            msg="获取服务人员详情成功"
        )
    except Exception as e:
        logger.error(f"获取服务人员详情失败: {str(e)}")
        return ResponseUtil.failure(msg="获取服务人员详情失败")


@staff_controller.post("/offline", summary="下架员工")
async def offline_staff(
    request_data: StaffStatusUpdateRequest,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    下架员工接口

    将员工状态修改为下架状态（status=2），下架后员工无法接收新订单

    Args:
        request_data: 包含员工ID和状态的请求数据
        db: 数据库会话
        current_user: 当前登录用户

    Returns:
        操作结果
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        company_id = current_user.user.company_id

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            return ResponseUtil.failure(msg="当前用户ID获取失败")

        if not company_id:
            logger.warning("当前用户没有关联的公司信息")
            return ResponseUtil.failure(msg="用户没有关联的公司信息")

        logger.info(f"用户 {current_user_id} 请求下架员工，员工ID: {request_data.staff_id}, 状态: {request_data.status}")
        logger.info(f"请求数据详情: staff_id={request_data.staff_id} (类型: {type(request_data.staff_id)}), status={request_data.status} (类型: {type(request_data.status)})")

        # 调用服务层下架员工
        from module_admin.service.staff_management_service import StaffManagementService
        result = await StaffManagementService.update_staff_status(
            db=db,
            staff_id=request_data.staff_id,
            status=request_data.status,
            operator_id=current_user_id,
            company_id=company_id
        )

        if result:
            logger.info(f"员工下架成功，员工ID: {request_data.staff_id}")
            return ResponseUtil.success(
                data={"staff_id": request_data.staff_id, "status": request_data.status},
                msg="员工下架成功"
            )
        else:
            logger.warning(f"员工下架失败，员工ID: {request_data.staff_id}")
            return ResponseUtil.failure(msg="员工下架失败，请检查员工ID是否正确")

    except Exception as e:
        logger.error(f"下架员工失败: {str(e)}")
        return ResponseUtil.failure(msg=f"下架员工失败: {str(e)}")


@staff_controller.post("/online", summary="上架员工")
async def online_staff(
    request_data: StaffStatusUpdateRequest,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    上架员工接口

    将员工状态修改为正常状态（status=1），上架后员工可以接收新订单

    Args:
        request_data: 包含员工ID和状态的请求数据
        db: 数据库会话
        current_user: 当前登录用户

    Returns:
        操作结果
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        company_id = current_user.user.company_id

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            return ResponseUtil.failure(msg="当前用户ID获取失败")

        if not company_id:
            logger.warning("当前用户没有关联的公司信息")
            return ResponseUtil.failure(msg="用户没有关联的公司信息")

        logger.info(f"用户 {current_user_id} 请求上架员工，员工ID: {request_data.staff_id}, 状态: {request_data.status}")
        logger.info(f"请求数据详情: staff_id={request_data.staff_id} (类型: {type(request_data.staff_id)}), status={request_data.status} (类型: {type(request_data.status)})")

        # 调用服务层上架员工
        from module_admin.service.staff_management_service import StaffManagementService
        result = await StaffManagementService.update_staff_status(
            db=db,
            staff_id=request_data.staff_id,
            status=request_data.status,
            operator_id=current_user_id,
            company_id=company_id
        )

        if result:
            logger.info(f"员工上架成功，员工ID: {request_data.staff_id}")
            return ResponseUtil.success(
                data={"staff_id": request_data.staff_id, "status": request_data.status},
                msg="员工上架成功"
            )
        else:
            logger.warning(f"员工上架失败，员工ID: {request_data.staff_id}")
            return ResponseUtil.failure(msg="员工上架失败，请检查员工ID是否正确")

    except Exception as e:
        logger.error(f"上架员工失败: {str(e)}")
        return ResponseUtil.failure(msg=f"上架员工失败: {str(e)}")


@staff_controller.get("/products/{staff_id}", summary="获取员工绑定产品")
async def get_staff_products(
    staff_id: str,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    获取员工绑定产品接口

    获取指定员工当前绑定的所有产品技能

    Args:
        staff_id: 员工ID
        db: 数据库会话
        current_user: 当前登录用户

    Returns:
        员工绑定的产品列表
    """
    try:
        logger.info(f"获取员工绑定产品，员工ID: {staff_id}")

        # 调用服务层获取员工绑定产品
        from module_admin.service.staff_service import StaffService
        result = await StaffService.get_staff_products_service(query_db=db, staff_id=staff_id)

        return ResponseUtil.success(
            data=result,
            msg="获取员工绑定产品成功"
        )
    except Exception as e:
        logger.error(f"获取员工绑定产品失败: {str(e)}")
        return ResponseUtil.failure(msg=f"获取员工绑定产品失败: {str(e)}")


@staff_controller.post("/products/{staff_id}", summary="更新员工产品绑定")
async def update_staff_products(
    staff_id: str,
    product_ids: List[int] = Body(..., description="产品ID列表"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    更新员工产品绑定接口

    更新员工的产品绑定关系（新增/删除）

    Args:
        staff_id: 员工ID
        product_ids: 产品ID列表
        db: 数据库会话
        current_user: 当前登录用户

    Returns:
        更新结果
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        company_id = current_user.user.company_id
        store_uuid = current_user.user.store_uuid

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            return ResponseUtil.failure(msg="当前用户ID获取失败")

        logger.info(f"用户 {current_user_id} 更新员工产品绑定，员工ID: {staff_id}, 产品IDs: {product_ids}")

        # 调用服务层更新员工产品绑定
        from module_admin.service.staff_service import StaffService
        result = await StaffService.update_staff_products_service(
            query_db=db,
            staff_id=staff_id,
            product_ids=product_ids,
            company_id=company_id,
            store_uuid=store_uuid
        )

        return ResponseUtil.success(
            data=result,
            msg="更新员工产品绑定成功"
        )
    except Exception as e:
        logger.error(f"更新员工产品绑定失败: {str(e)}")
        return ResponseUtil.failure(msg=f"更新员工产品绑定失败: {str(e)}")


@staff_controller.get("/available-products", summary="获取门店可用产品")
async def get_available_products(
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    获取门店可用产品接口

    获取当前门店所有可用的产品列表

    Args:
        db: 数据库会话
        current_user: 当前登录用户

    Returns:
        门店可用产品列表
    """
    try:
        # 获取当前用户信息
        company_id = current_user.user.company_id

        if not company_id:
            logger.warning("当前用户没有关联的公司信息")
            return ResponseUtil.failure(msg="用户没有关联的公司信息")

        logger.info(f"获取门店可用产品，公司ID: {company_id}")

        # 调用服务层获取门店可用产品
        from module_admin.service.staff_service import StaffService
        result = await StaffService.get_available_products_service(query_db=db, company_id=company_id)

        return ResponseUtil.success(
            data=result,
            msg="获取门店可用产品成功"
        )
    except Exception as e:
        logger.error(f"获取门店可用产品失败: {str(e)}")
        return ResponseUtil.failure(msg=f"获取门店可用产品失败: {str(e)}")


# 为了兼容可能的导入，添加驼峰命名风格的别名
staffController = staff_controller
