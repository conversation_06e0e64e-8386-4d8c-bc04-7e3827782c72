class BaseException(Exception):
    """
    基础异常类
    """
    def __init__(self, message: str = "系统异常", code: int = 500, data: any = None):
        self.message = message
        self.code = code
        self.data = data
        super().__init__(self.message)

# 数据库相关异常
class DatabaseException(BaseException):
    """
    数据库异常
    """
    def __init__(self, message: str = "数据库操作异常", code: int = 5001, data: any = None):
        super().__init__(message, code, data)

class QueryException(DatabaseException):
    """
    查询异常
    """
    def __init__(self, message: str = "数据查询异常", code: int = 5002, data: any = None):
        super().__init__(message, code, data)

class UpdateException(DatabaseException):
    """
    更新异常
    """
    def __init__(self, message: str = "数据更新异常", code: int = 5003, data: any = None):
        super().__init__(message, code, data)

class DeleteException(DatabaseException):
    """
    删除异常
    """
    def __init__(self, message: str = "数据删除异常", code: int = 5004, data: any = None):
        super().__init__(message, code, data)

class InsertException(DatabaseException):
    """
    插入异常
    """
    def __init__(self, message: str = "数据插入异常", code: int = 5005, data: any = None):
        super().__init__(message, code, data)

# 业务逻辑相关异常
class BusinessException(BaseException):
    """
    业务逻辑异常
    """
    def __init__(self, message: str = "业务逻辑异常", code: int = 4001, data: any = None):
        super().__init__(message, code, data)

class ValidationException(BusinessException):
    """
    数据验证异常
    """
    def __init__(self, message: str = "数据验证失败", code: int = 4002, data: any = None):
        super().__init__(message, code, data)

class ResourceNotFoundException(BusinessException):
    """
    资源不存在异常
    """
    def __init__(self, message: str = "请求的资源不存在", code: int = 4004, data: any = None):
        super().__init__(message, code, data)

class DuplicateResourceException(BusinessException):
    """
    资源重复异常
    """
    def __init__(self, message: str = "资源已存在", code: int = 4009, data: any = None):
        super().__init__(message, code, data)

# 认证授权相关异常
class AuthException(BaseException):
    """
    认证异常
    """
    def __init__(self, message: str = "认证失败", code: int = 4010, data: any = None):
        super().__init__(message, code, data)

class LoginException(AuthException):
    """
    登录异常
    """
    def __init__(self, message: str = "登录失败", code: int = 4011, data: any = None):
        super().__init__(message, code, data)

class TokenException(AuthException):
    """
    Token异常
    """
    def __init__(self, message: str = "Token无效或已过期", code: int = 4012, data: any = None):
        super().__init__(message, code, data)

class PermissionException(AuthException):
    """
    权限异常
    """
    def __init__(self, message: str = "没有操作权限", code: int = 4013, data: any = None):
        super().__init__(message, code, data)

# 外部服务相关异常
class ExternalServiceException(BaseException):
    """
    外部服务异常
    """
    def __init__(self, message: str = "外部服务调用异常", code: int = 5010, data: any = None):
        super().__init__(message, code, data)

class HttpRequestException(ExternalServiceException):
    """
    HTTP请求异常
    """
    def __init__(self, message: str = "HTTP请求异常", code: int = 5011, data: any = None):
        super().__init__(message, code, data)

class ThirdPartyAPIException(ExternalServiceException):
    """
    第三方API异常
    """
    def __init__(self, message: str = "第三方API调用异常", code: int = 5012, data: any = None):
        super().__init__(message, code, data)

# 兼容旧版异常
class ServiceException(BusinessException):
    """
    自定义服务异常ServiceException
    """
    def __init__(self, data: any = None, message: str = "服务异常"):
        super().__init__(message=message, code=4001, data=data)

class ServiceWarning(BusinessException):
    """
    自定义服务警告ServiceWarning
    """
    def __init__(self, data: any = None, message: str = "服务警告"):
        super().__init__(message=message, code=4002, data=data)

class ModelValidatorException(ValidationException):
    """
    自定义模型校验异常ModelValidatorException
    """
    def __init__(self, data: any = None, message: str = "模型校验失败"):
        super().__init__(message=message, code=4003, data=data)
