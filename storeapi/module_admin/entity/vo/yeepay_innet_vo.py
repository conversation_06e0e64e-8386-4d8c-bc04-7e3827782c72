"""
易宝支付入网相关的VO模型
"""
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator, model_validator


class InnetOwnerVO(BaseModel):
    """入网主体信息"""
    ownerType: Optional[str] = Field(None, description="主体类型：01-企业，02-个体工商户")
    ownerName: str = Field(..., description="主体名称")
    ownerShortName: Optional[str] = Field(None, description="主体简称")
    ownerCertNo: Optional[str] = Field(None, description="证件号码（统一社会信用代码）")
    ownerCertPic: Optional[str] = Field(None, description="证件照片文件ID")
    ownerCertType: Optional[str] = Field("L01", description="证件类型，默认L01（营业执照）")


class InnetJuridicalVO(BaseModel):
    """法人信息"""
    juridicalName: str = Field(..., description="法人姓名")
    juridicalCertNo: str = Field(..., description="法人证件号码")
    juridicalCertType: Optional[str] = Field("P01", description="法人证件类型，默认P01（身份证）")
    juridicalCertPic: str = Field(..., description="法人证件人像面文件ID")
    juridicalCertBackPic: str = Field(..., description="法人证件非人像面文件ID")
    juridicalMobile: Optional[str] = Field(None, description="法人手机号")


class InnetContactVO(BaseModel):
    """联系人信息"""
    contactName: str = Field(..., description="联系人姓名")
    contactMobile: str = Field(..., description="联系人手机号")
    contactEmail: Optional[str] = Field(None, description="联系人邮箱")


class InnetAddressVO(BaseModel):
    """经营地址信息"""
    province: str = Field(..., description="省份")
    city: str = Field(..., description="城市")
    district: str = Field(..., description="区县")
    address: str = Field(..., description="详细地址")


class InnetAccountVO(BaseModel):
    """结算账户信息"""
    accountType: str = Field(..., description="账户类型：01-个人借记卡，02-对公一般户，03-企业单位结算卡")
    accountNo: str = Field(..., description="账号")
    accountName: str = Field(..., description="账户名称")
    bankCode: str = Field(..., description="开户行代码")
    bankName: Optional[str] = Field(None, description="开户行名称")
    settleCycle: Optional[str] = Field("T1", description="结算周期，默认T1")


class InnetProductVO(BaseModel):
    """产品信息"""
    productCode: str = Field(..., description="产品代码")
    productName: Optional[str] = Field(None, description="产品名称")
    settleCycle: Optional[str] = Field("T1", description="结算周期")
    
    
class InnetProductRateVO(BaseModel):
    """产品费率信息"""
    productCode: str = Field(..., description="产品代码")
    chargeScene: str = Field(..., description="计费场景")
    rateType: str = Field(..., description="费率类型")
    rateValue: str = Field(..., description="费率值")


class EnterpriseInnetRequestVO(BaseModel):
    """企业/个体户入网请求"""
    innetOwner: InnetOwnerVO = Field(..., description="入网主体信息")
    innetJuridical: InnetJuridicalVO = Field(..., description="法人信息")
    innetContact: InnetContactVO = Field(..., description="联系人信息")
    innetAddress: Optional[InnetAddressVO] = Field(None, description="经营地址信息")
    innetAccount: Optional[InnetAccountVO] = Field(None, description="结算账户信息")
    innetProduct: Optional[List[InnetProductVO]] = Field(None, description="产品信息")
    innetProductRate: Optional[List[InnetProductRateVO]] = Field(None, description="产品费率信息")
    businessMode: Optional[str] = Field("cus", description="业务模式，默认cus")
    
    @validator('innetOwner')
    def validate_owner(cls, v):
        if v.ownerType in ['01', '02'] and not v.ownerCertNo:
            raise ValueError('企业和个体工商户必须提供证件号码')
        if v.ownerType in ['01', '02'] and not v.ownerCertPic:
            raise ValueError('企业和个体工商户必须提供证件照片')
        return v


class MicroInnetRequestVO(BaseModel):
    """小微/个人入网请求"""
    innetOwner: InnetOwnerVO = Field(..., description="入网主体信息")
    innetJuridical: InnetJuridicalVO = Field(..., description="法人信息")
    innetAddress: Optional[InnetAddressVO] = Field(None, description="经营地址信息")
    innetAccount: Optional[InnetAccountVO] = Field(None, description="结算账户信息")
    businessMode: Optional[str] = Field("cus", description="业务模式，默认cus")
    
    @validator('innetOwner')
    def validate_owner(cls, v):
        if not v.ownerName:
            raise ValueError('主体名称不能为空')
        if not v.ownerShortName:
            raise ValueError('主体简称不能为空')
        return v


class InnetQueryRequestVO(BaseModel):
    """入网结果查询请求"""
    origTxnDate: str = Field(..., description="入网请求日期，格式：yyyyMMdd")
    origCusTraceNo: Optional[str] = Field(None, description="入网请求的客户流水号")
    origSysTraceNo: Optional[str] = Field(None, description="入网请求的系统跟踪号")

    @validator('origTxnDate')
    def validate_orig_txn_date(cls, v):
        if not v:
            raise ValueError('入网请求日期不能为空')
        if len(v) != 8 or not v.isdigit():
            raise ValueError('入网请求日期格式错误，应为yyyyMMdd')
        return v

    @model_validator(mode='after')
    def validate_trace_numbers(self):
        if not self.origCusTraceNo and not self.origSysTraceNo:
            raise ValueError('客户流水号和系统跟踪号不能同时为空')
        return self


class FileUploadRequestVO(BaseModel):
    """文件上传请求"""
    fileName: str = Field(..., description="文件名")
    fileType: str = Field(..., description="文件类型")


class InnetResponseVO(BaseModel):
    """入网响应"""
    rspCode: str = Field(..., description="响应码")
    rspMsg: str = Field(..., description="响应消息")
    innetCusCode: Optional[str] = Field(None, description="入网客户代码")
    orderDate: Optional[str] = Field(None, description="订单日期")
    cusTraceNo: Optional[str] = Field(None, description="客户请求流水号")


class FileUploadResponseVO(BaseModel):
    """文件上传响应"""
    rspCode: str = Field(..., description="响应码")
    rspMsg: str = Field(..., description="响应消息")
    fileId: Optional[str] = Field(None, description="文件ID")


class InnetQueryResponseVO(BaseModel):
    """入网查询响应"""
    rspCode: str = Field(..., description="响应码")
    rspMsg: str = Field(..., description="响应消息")
    innetStatus: Optional[str] = Field(None, description="入网状态")
    innetCusCode: Optional[str] = Field(None, description="入网客户代码")
    auditMsg: Optional[str] = Field(None, description="审核信息")


# 常量定义
class InnetConstants:
    """入网相关常量"""
    
    # 主体类型
    OWNER_TYPE_ENTERPRISE = "01"  # 企业
    OWNER_TYPE_INDIVIDUAL = "02"  # 个体工商户
    
    # 证件类型
    CERT_TYPE_BUSINESS_LICENSE = "L01"  # 营业执照
    CERT_TYPE_ID_CARD = "P01"  # 身份证
    
    # 账户类型
    ACCOUNT_TYPE_PERSONAL_DEBIT = "01"  # 个人借记卡
    ACCOUNT_TYPE_CORPORATE_GENERAL = "02"  # 对公一般户
    ACCOUNT_TYPE_ENTERPRISE_SETTLEMENT = "03"  # 企业单位结算卡
    
    # 结算周期
    SETTLE_CYCLE_T0 = "T0"  # T+0
    SETTLE_CYCLE_T1 = "T1"  # T+1
    
    # 业务模式
    BUSINESS_MODE_CUS = "cus"  # 客户模式
    
    # 响应码
    RSP_CODE_SUCCESS = "0000"  # 成功
    
    # 入网状态
    INNET_STATUS_PENDING = "01"  # 待审核
    INNET_STATUS_APPROVED = "02"  # 审核通过
    INNET_STATUS_REJECTED = "03"  # 审核拒绝
    
    # 文件类型
    FILE_TYPE_IMAGE = "image"  # 图片
    FILE_TYPE_PDF = "pdf"  # PDF文档
