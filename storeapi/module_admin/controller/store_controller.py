"""
门店管理控制器
"""
from fastapi import APIRouter, Depends, Request, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional

from config.get_db import get_db
from module_admin.service.auth_adapter import AuthAdapter
from module_admin.service.internal_user_login_service import InternalUserLoginService
from module_admin.service.store_service import StoreService
from module_admin.entity.vo.user_vo import CurrentUserModel
from utils.response_util import ResponseUtil
from utils.log_util import logger
from exceptions.exception import ValidationException, BusinessException, QueryException, ResourceNotFoundException


# 使用统一的API路由前缀格式
store_controller = APIRouter(prefix='/api/v1/store', dependencies=[Depends(AuthAdapter.get_current_user)])


@store_controller.get('/list', summary="获取门店列表")
async def get_store_list(
    request: Request,
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    status: Optional[int] = Query(None, description="门店状态筛选（0-关闭，1-营业）"),
    current_user = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取门店列表接口
    
    支持分页和状态筛选，只返回当前公司的门店数据
    
    Args:
        request: FastAPI请求对象
        page: 页码
        size: 每页数量
        status: 门店状态筛选
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        门店列表响应数据
    """
    try:
        # 获取当前用户的公司ID
        company_id = current_user.user.company_id
        
        if not company_id:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")
        
        logger.info(f"获取门店列表，公司ID: {company_id}, 页码: {page}, 每页: {size}, 状态: {status}")
        
        # 调用服务层获取门店列表
        result = await StoreService.get_store_list_service(
            query_db, company_id, page, size, status
        )
        
        return ResponseUtil.success(data=result, msg="获取门店列表成功")
        
    except ValidationException as e:
        logger.error(f"获取门店列表参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except QueryException as e:
        logger.error(f"获取门店列表查询异常: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except BusinessException as e:
        logger.error(f"获取门店列表业务异常: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"获取门店列表未知异常: {str(e)}")
        return ResponseUtil.error(msg="获取门店列表失败")


@store_controller.get('/detail', summary="获取门店详情")
async def get_store_detail(
    request: Request,
    store_uuid: str = Query(..., description="门店UUID"),
    current_user = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取门店详情接口
    
    根据门店UUID获取门店详细信息，确保只能查看当前公司的门店
    
    Args:
        request: FastAPI请求对象
        store_uuid: 门店UUID
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        门店详情响应数据
    """
    try:
        # 获取当前用户的公司ID
        company_id = current_user.user.company_id
        
        if not company_id:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")
        
        logger.info(f"获取门店详情，门店UUID: {store_uuid}, 公司ID: {company_id}")
        
        # 调用服务层获取门店详情
        result = await StoreService.get_store_detail_service(
            query_db, store_uuid, company_id
        )
        
        return ResponseUtil.success(data=result, msg="获取门店详情成功")
        
    except ValidationException as e:
        logger.error(f"获取门店详情参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except ResourceNotFoundException as e:
        logger.error(f"门店不存在: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except QueryException as e:
        logger.error(f"获取门店详情查询异常: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except BusinessException as e:
        logger.error(f"获取门店详情业务异常: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"获取门店详情未知异常: {str(e)}")
        return ResponseUtil.error(msg="获取门店详情失败")


@store_controller.get('/count', summary="获取门店统计")
async def get_store_count(
    request: Request,
    current_user = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取门店统计接口
    
    获取当前公司的门店统计信息，包括总数、营业中数量、已关闭数量
    
    Args:
        request: FastAPI请求对象
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        门店统计响应数据
    """
    try:
        # 获取当前用户的公司ID
        company_id = current_user.user.company_id
        
        if not company_id:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")
        
        logger.info(f"获取门店统计，公司ID: {company_id}")
        
        # 调用服务层获取门店统计
        result = await StoreService.get_store_count_service(query_db, company_id)
        
        return ResponseUtil.success(data=result, msg="获取门店统计成功")
        
    except ValidationException as e:
        logger.error(f"获取门店统计参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except QueryException as e:
        logger.error(f"获取门店统计查询异常: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except BusinessException as e:
        logger.error(f"获取门店统计业务异常: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"获取门店统计未知异常: {str(e)}")
        return ResponseUtil.error(msg="获取门店统计失败")
