# 微信小程序转发功能配置文档

## 功能概述

为金刚到家小程序的商家端和员工端添加微信转发功能。所有转发都指向小程序的登录入口页面，便于新用户快速进入小程序。

## 已配置转发功能的页面

### 商家端
- **登录页面** (`src/pages/login/login.vue`) - 支持转发
- **首页** (`src/pages/home/<USER>

### 员工端  
- **登录页面** (`src/pages-staff/login/index.vue`) - 支持转发
- **首页** (`src/pages-staff/home/<USER>

### 全局配置
- **App.vue** - 全局转发配置，作为兜底方案

## 转发配置内容

所有页面使用统一的转发配置：

```javascript
onShareAppMessage() {
  let shareobj = {
    title: '家政服务好帮手，进来逛逛吧~', //分享的标题
    path: '/pages/index/index?tg=' + uni.getStorageSync('tg') + '&shareScene=' + uni.getStorageSync('scene'), //好友点击分享之后跳转的页面
    imageUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jingang/shareCover/share_02.png", //分享的图片
  };
  return shareobj;
}
```

## 转发内容说明

- **标题**：`家政服务好帮手，进来逛逛吧~`
- **跳转路径**：`/pages/index/index` (登录入口页面)
- **URL参数**：
  - `tg`: 从本地存储获取的推广参数
  - `shareScene`: 从本地存储获取的分享场景参数
- **分享图片**：使用统一的分享封面图片

## 用户使用流程

### 分享方操作
1. 在微信小程序中点击右上角"..."按钮
2. 选择"转发给朋友"
3. 选择要发送的微信好友或群聊
4. 添加转发消息（可选）
5. 点击发送

### 接收方体验
1. 在微信中收到小程序分享卡片
2. 点击卡片进入小程序登录入口页面
3. 可以选择商家端或员工端进行登录
4. 开始使用小程序功能

## 技术实现

### 生命周期方法
使用 `onShareAppMessage` 生命周期方法实现转发功能。

### 参数获取
- 通过 `uni.getStorageSync('tg')` 获取推广参数
- 通过 `uni.getStorageSync('scene')` 获取分享场景参数

### 统一配置
创建了 `src/mixins/shareConfig.js` 文件，提供统一的转发配置，方便其他页面复用。

## 注意事项

1. **分享图片**：使用网络图片，确保图片链接有效
2. **URL参数**：保持与现有业务逻辑的兼容性
3. **跳转路径**：统一指向登录入口页面，避免用户迷失
4. **兼容性**：确保在不同微信版本中都能正常工作

## 扩展功能

### 分享到朋友圈
如果需要支持分享到朋友圈，可以添加 `onShareTimeline` 方法：

```javascript
onShareTimeline() {
  return {
    title: '家政服务好帮手，进来逛逛吧~',
    imageUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jingang/shareCover/share_02.png"
  };
}
```

### 数据统计
可以在转发方法中添加统计代码，记录转发行为：

```javascript
onShareAppMessage() {
  // 记录转发行为
  console.log('用户触发转发功能');
  
  // 返回转发配置
  let shareobj = {
    // ... 转发配置
  };
  return shareobj;
}
```

## 测试验证

### 测试场景
1. **商家端登录页转发** - 验证转发功能正常
2. **商家端首页转发** - 验证转发功能正常  
3. **员工端登录页转发** - 验证转发功能正常
4. **员工端首页转发** - 验证转发功能正常
5. **接收方点击** - 验证能正确跳转到登录入口页面

### 预期结果
- 转发功能在所有配置页面正常工作
- 转发内容符合预期（标题、图片、路径）
- 接收方能正常打开小程序并进入登录页面
- URL参数正确传递
