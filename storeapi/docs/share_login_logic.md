# 微信小程序分享登录逻辑实现文档

## 需求说明

根据用户要求，分享给朋友后朋友打开小程序的处理逻辑：

1. **已登录用户**：无论是商家端还是员工端，都自动载入本地缓存数据跳转到对应首页
2. **未登录用户**：可以自己选择商家端或员工端进行登录

## 实现方案

### 1. App.vue 启动逻辑修改

**修改内容**：
- 移除分享进入时的状态清除逻辑
- 保持正常的状态恢复机制
- 已登录用户自动跳转到对应首页
- 未登录用户跳转到登录选择页

**关键代码**：
```javascript
onLaunch(options) {
  // 检查是否是通过分享进入的
  const isFromShare = options && (options.scene === 1007 || options.scene === 1008 || options.scene === 1044 || options.query);
  
  // 恢复本地状态到Vuex（无论是否分享进入）
  // 已登录用户自动跳转到对应首页
  // 未登录用户跳转到登录选择页
}
```

### 2. 登录页面逻辑修改

**修改内容**：
- 检查用户登录状态
- 已登录用户直接跳转到对应首页
- 未登录用户显示登录选择界面

**关键代码**：
```javascript
onLoad(option) {
  // 检查用户是否已经登录
  const isLogin = uni.getStorageSync('isLogin');
  const token = uni.getStorageSync('token');
  const staffToken = uni.getStorageSync('staffToken');
  const currentRole = uni.getStorageSync('currentRole');
  
  // 如果用户已经登录，直接跳转到对应首页
  if (isLogin && (token || staffToken)) {
    if (currentRole === 'staff' && staffToken) {
      uni.reLaunch({ url: '/pages-staff/home/<USER>' });
    } else if (token) {
      uni.reLaunch({ url: '/pages/home/<USER>' });
    }
    return;
  }
  
  // 未登录用户，显示登录选择界面
  this.selectedRole = 'store';
}
```

### 3. 首页角色检查恢复

**修改内容**：
- 恢复正常的角色检查逻辑
- 移除分享进入的特殊处理
- 保持原有的角色跳转机制

**关键代码**：
```javascript
checkUserRoleAndRedirect() {
  // 正常的角色检查逻辑
  const currentRole = uni.getStorageSync('currentRole');
  const staffInfo = uni.getStorageSync('staffInfo');
  const staffToken = uni.getStorageSync('staffToken');

  // 如果是员工角色，跳转到员工端首页
  if (currentRole === 'staff' || staffInfo || staffToken) {
    uni.reLaunch({ url: '/pages-staff/home/<USER>' });
    return;
  }

  // 否则加载管理端数据
  this.loadUserInfo();
}
```

## 用户体验流程

### 场景1：已登录商家端用户通过分享进入

1. 用户点击分享链接
2. 小程序启动，检测到用户已登录商家端
3. 自动跳转到商家端首页
4. 载入本地缓存数据

### 场景2：已登录员工端用户通过分享进入

1. 用户点击分享链接
2. 小程序启动，检测到用户已登录员工端
3. 自动跳转到员工端首页
4. 载入本地缓存数据

### 场景3：未登录用户通过分享进入

1. 用户点击分享链接
2. 小程序启动，检测到用户未登录
3. 跳转到登录选择页面
4. 用户可以选择商家端或员工端进行登录

### 场景4：正常启动（非分享）

1. 用户直接打开小程序
2. 按照原有逻辑进行认证检查和页面跳转
3. 保持原有的用户体验

## 技术要点

### 1. 状态管理
- 保持本地存储的完整性
- 不因分享进入而清除用户状态
- 正确恢复Vuex状态

### 2. 角色判断
- 优先检查 `currentRole` 字段
- 兼容检查 `staffInfo` 和 `staffToken`
- 确保角色跳转的准确性

### 3. 登录检查
- 检查 `isLogin` 状态
- 验证对应的 token 存在
- 确保登录状态的有效性

### 4. 页面跳转
- 使用 `uni.reLaunch` 确保页面栈清理
- 根据角色跳转到正确的首页
- 避免重复跳转和循环跳转

## 测试验证

### 测试场景
1. **已登录商家端用户分享测试**
   - 分享链接给其他已登录商家端的用户
   - 验证接收方直接进入商家端首页

2. **已登录员工端用户分享测试**
   - 分享链接给其他已登录员工端的用户
   - 验证接收方直接进入员工端首页

3. **跨角色分享测试**
   - 商家端用户分享给员工端用户
   - 员工端用户分享给商家端用户
   - 验证接收方进入对应的首页

4. **未登录用户分享测试**
   - 分享链接给未登录用户
   - 验证接收方进入登录选择页面

5. **正常启动测试**
   - 验证非分享启动的正常流程
   - 确保原有功能不受影响

### 预期结果
- 已登录用户：自动进入对应首页，载入本地数据
- 未登录用户：进入登录选择页面，可自由选择登录方式
- 正常启动：保持原有的用户体验
- 无异常跳转：避免循环跳转和错误跳转

## 注意事项

1. **数据一致性**：确保本地存储和Vuex状态的一致性
2. **角色准确性**：正确识别用户角色，避免错误跳转
3. **登录有效性**：验证token的有效性，确保登录状态正确
4. **用户体验**：减少不必要的页面跳转，提升用户体验
5. **兼容性**：保持与现有功能的兼容性，不影响正常使用
