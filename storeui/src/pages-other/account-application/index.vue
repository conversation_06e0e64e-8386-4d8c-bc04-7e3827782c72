<template>
  <view class="application-container">
    <appHead title="个人开户申请"></appHead>
    
    <view class="content">
      <!-- 申请说明 -->
      <view class="section">
        <view class="section-title">开户说明</view>
        <view class="tips-box">
          <text class="tips-text">💡 上传证件照片后系统将自动识别并填充信息，请核对后提交申请</text>
        </view>
      </view>

      <!-- 身份证照片上传 -->
      <view class="section">
        <view class="section-title">身份证照片上传</view>
        <view class="tips-box">
          <text class="tips-text">📷 请先上传身份证正反面照片，系统将自动识别并填充个人信息</text>
        </view>

        <view class="form-item">
          <view class="label">身份证人像面 <text class="required">*</text></view>
          <view class="upload-area" @click="uploadIdCardFront">
            <image
              v-if="formData.idCardFrontUrl"
              :src="formData.idCardFrontUrl"
              class="upload-image"
            />
            <view v-else class="upload-placeholder">
              <text class="upload-icon">📷</text>
              <text class="upload-text">点击上传身份证人像面</text>
            </view>
          </view>
        </view>

        <view class="form-item">
          <view class="label">身份证国徽面 <text class="required">*</text></view>
          <view class="upload-area" @click="uploadIdCardBack">
            <image
              v-if="formData.idCardBackUrl"
              :src="formData.idCardBackUrl"
              class="upload-image"
            />
            <view v-else class="upload-placeholder">
              <text class="upload-icon">📷</text>
              <text class="upload-text">点击上传身份证国徽面</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 银行卡照片上传 -->
      <view class="section">
        <view class="section-title">银行卡照片上传</view>
        <view class="tips-box">
          <text class="tips-text">💳 请上传银行卡照片，系统将自动识别并填充银行信息</text>
        </view>

        <view class="form-item">
          <view class="label">银行卡照片 <text class="required">*</text></view>
          <view class="upload-area" @click="uploadBankCard">
            <image
              v-if="formData.bankCardUrl"
              :src="formData.bankCardUrl"
              class="upload-image"
            />
            <view v-else class="upload-placeholder">
              <text class="upload-icon">💳</text>
              <text class="upload-text">点击上传银行卡照片</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 识别状态提示 -->
      <view class="section" v-if="ocrRecognized">
        <view class="success-tips">
          <text class="success-icon">✅</text>
          <text class="success-text">所有证件识别完成，可以提交申请</text>
        </view>
      </view>

      
      <!-- 提交按钮 -->
      <view class="submit-section">
        <button 
          class="submit-btn" 
          :class="{ disabled: submitting }"
          @click="submitApplication"
          :disabled="submitting"
        >
          {{ submitting ? '提交中...' : '提交开户申请' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { submitAccountApplication, recognizeIdCardFrontFromTempFile, recognizeIdCardBackFromTempFile, recognizeBankCardFromTempFile, recognizeBankCard } from '@/api/account-application.js'
import {
  submitStaffAccountApplication,
  recognizeStaffIdCardFrontFromTempFile,
  recognizeStaffIdCardBackFromTempFile,
  recognizeStaffBankCardFromTempFile,
  recognizeStaffIdCardFrontFromUrl,
  recognizeStaffIdCardBackFromUrl,
  recognizeStaffBankCardFromUrl
} from '@/api/staff-account-application.js'

export default {
  data() {
    return {
      submitting: false,
      ocrRecognized: false, // OCR识别状态

      // 表单数据
      formData: {
        // 基本信息
        mobile: '',
        name: '',
        briefName: '', // 主体简称
        idNumber: '',
        gender: '',
        birthday: '',

        // 银行信息
        bankName: '',
        accountNo: '',
        accountName: '',

        // 照片URL
        idCardFrontUrl: '',
        idCardBackUrl: '',
        bankCardUrl: ''
      },

      // OCR识别结果
      ocrResults: {
        idCardFront: null,  // 身份证正面识别结果
        idCardBack: null,   // 身份证背面识别结果
        bankCard: null      // 银行卡识别结果
      },

      // 性别选项
      genderOptions: [
        { value: '男', text: '男' },
        { value: '女', text: '女' }
      ],
      genderIndex: -1
    }
  },

  onLoad() {
    // 获取当前用户手机号
    this.getCurrentUserMobile()
  },
  
  methods: {
    // 获取当前用户手机号
    getCurrentUserMobile() {
      try {
        const userInfo = uni.getStorageSync('userInfo')
        if (userInfo && userInfo.mobile) {
          this.formData.mobile = userInfo.mobile
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    },

    // 获取合适的token
    getToken() {
      // 智能选择token：优先使用员工端token，其次使用管理端token
      const currentRole = uni.getStorageSync('currentRole') || 'store'

      if (currentRole === 'staff') {
        // 员工端：优先使用access_token，其次使用staffToken
        const accessToken = uni.getStorageSync('access_token')
        const staffToken = uni.getStorageSync('staffToken')
        return accessToken || staffToken || uni.getStorageSync('token') || ''
      } else {
        // 管理端：使用普通token
        return uni.getStorageSync('token') || ''
      }
    },

    // 获取当前角色
    getCurrentRole() {
      return uni.getStorageSync('currentRole') || 'store'
    },

    // 根据角色选择合适的API
    getApiByRole() {
      const currentRole = this.getCurrentRole()

      if (currentRole === 'staff') {
        return {
          submitApplication: submitStaffAccountApplication,
          recognizeIdCardFront: recognizeStaffIdCardFrontFromUrl,
          recognizeIdCardBack: recognizeStaffIdCardBackFromUrl,
          recognizeBankCard: recognizeStaffBankCardFromUrl
        }
      } else {
        return {
          submitApplication: submitAccountApplication,
          recognizeIdCardFront: recognizeIdCardFrontFromTempFile,
          recognizeIdCardBack: recognizeIdCardBackFromTempFile,
          recognizeBankCard: recognizeBankCard
        }
      }
    },

    // 性别选择
    onGenderChange(e) {
      this.genderIndex = e.detail.value
      this.formData.gender = this.genderOptions[e.detail.value].value
    },

    // 生日选择
    onBirthdayChange(e) {
      this.formData.birthday = e.detail.value
    },

    // 上传身份证正面
    uploadIdCardFront() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['camera', 'album'],
        success: async (res) => {
          try {
            const tempFilePath = res.tempFilePaths[0]
            uni.showLoading({ title: '上传识别中...' })

            // 上传文件并进行OCR识别
            const uploadResult = await this.uploadFile(tempFilePath)

            if (uploadResult && uploadResult.url) {
              this.formData.idCardFrontUrl = uploadResult.url

              // 立即进行OCR识别，使用上传后的URL
              const api = this.getApiByRole()
              const ocrResult = await api.recognizeIdCardFront(uploadResult.url)
              if (ocrResult && this.validateIdCardFrontResult(ocrResult)) {
                this.ocrResults.idCardFront = ocrResult
                this.checkAllOcrComplete()
                uni.showToast({ title: '身份证正面识别成功', icon: 'success' })
              } else {
                uni.showToast({ title: '识别失败，请重新上传', icon: 'none' })
                this.formData.idCardFrontUrl = ''
              }
            } else {
              uni.showToast({ title: '上传失败', icon: 'error' })
            }

            uni.hideLoading()
          } catch (error) {
            uni.hideLoading()
            console.error('上传身份证正面失败:', error)
            uni.showToast({ title: '操作失败', icon: 'error' })
          }
        }
      })
    },

    // 上传身份证背面
    uploadIdCardBack() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['camera', 'album'],
        success: async (res) => {
          try {
            const tempFilePath = res.tempFilePaths[0]
            uni.showLoading({ title: '上传识别中...' })

            // 上传文件并进行OCR识别
            const uploadResult = await this.uploadFile(tempFilePath)

            if (uploadResult && uploadResult.url) {
              this.formData.idCardBackUrl = uploadResult.url

              // 立即进行OCR识别，使用上传后的URL
              const api = this.getApiByRole()
              const ocrResult = await api.recognizeIdCardBack(uploadResult.url)
              if (ocrResult && this.validateIdCardBackResult(ocrResult)) {
                this.ocrResults.idCardBack = ocrResult
                this.checkAllOcrComplete()
                uni.showToast({ title: '身份证背面识别成功', icon: 'success' })
              } else {
                uni.showToast({ title: '识别失败，请重新上传', icon: 'none' })
                this.formData.idCardBackUrl = ''
              }
            } else {
              uni.showToast({ title: '上传失败', icon: 'error' })
            }

            uni.hideLoading()
          } catch (error) {
            uni.hideLoading()
            console.error('上传身份证背面失败:', error)
            uni.showToast({ title: '操作失败', icon: 'error' })
          }
        }
      })
    },

    // 上传银行卡照片
    uploadBankCard() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['camera', 'album'],
        success: async (res) => {
          try {
            const tempFilePath = res.tempFilePaths[0]
            uni.showLoading({ title: '上传识别中...' })

            // 上传文件并进行OCR识别
            const uploadResult = await this.uploadFile(tempFilePath)

            if (uploadResult && uploadResult.url) {
              this.formData.bankCardUrl = uploadResult.url

              // 立即进行OCR识别，使用上传后的URL
              const api = this.getApiByRole()
              const ocrResult = await api.recognizeBankCard(uploadResult.url)
              if (ocrResult && this.validateBankCardResult(ocrResult)) {
                this.ocrResults.bankCard = ocrResult
                this.checkAllOcrComplete()
                uni.showToast({ title: '银行卡识别成功', icon: 'success' })
              } else {
                uni.showToast({ title: '识别失败，请重新上传', icon: 'none' })
                this.formData.bankCardUrl = ''
              }
            } else {
              uni.showToast({ title: '上传失败', icon: 'error' })
            }

            uni.hideLoading()
          } catch (error) {
            uni.hideLoading()
            console.error('上传银行卡照片失败:', error)
            uni.showToast({ title: '操作失败', icon: 'error' })
          }
        }
      })
    },

    // 通用文件上传方法
    async uploadFile(filePath) {
      return new Promise((resolve, reject) => {
        // 根据角色选择合适的上传接口
        const currentRole = this.getCurrentRole()
        const uploadUrl = currentRole === 'staff'
          ? this.$baseUrl + '/api/v1/staff/common/upload'
          : this.$baseUrl + '/api/v1/common/upload'

        console.log(`使用${currentRole === 'staff' ? '员工端' : '管理端'}上传接口:`, uploadUrl)

        uni.uploadFile({
          url: uploadUrl,
          filePath: filePath,
          name: 'file',
          header: {
            'Authorization': 'Bearer ' + this.getToken()
          },
          success: (uploadRes) => {
            try {
              const result = JSON.parse(uploadRes.data)
              if (result.code === 200) {
                resolve(result.data)
              } else {
                reject(new Error(result.msg || '上传失败'))
              }
            } catch (error) {
              reject(error)
            }
          },
          fail: (error) => {
            reject(error)
          }
        })
      })
    },

    // 从临时文件进行身份证OCR识别
    async recognizeIdCardFromTempFile(tempFilePath) {
      try {
        if (!tempFilePath) {
          throw new Error('临时文件路径为空')
        }

        // 根据角色选择合适的API
        const api = this.getApiByRole()
        const result = await api.recognizeIdCardFront(tempFilePath)

        if (result && this.validateIdCardFrontResult(result)) {
          // 保存OCR识别结果
          this.ocrResults.idCardFront = result

          // 填充识别结果到表单
          this.formData.name = result.name || ''
          this.formData.briefName = result.name || '' // 主体简称默认使用姓名
          this.formData.idNumber = result.id_number || ''
          this.formData.gender = result.gender || ''
          this.formData.birthday = result.birthday || ''

          // 设置性别选择器的索引
          if (this.formData.gender) {
            const genderIndex = this.genderOptions.findIndex(item => item.value === this.formData.gender)
            if (genderIndex !== -1) {
              this.genderIndex = genderIndex
            }
          }

          this.checkAllOcrComplete()
          return true
        } else {
          // 识别失败，清空图片
          this.formData.idCardFrontUrl = ''
          this.ocrResults.idCardFront = null
          throw new Error('身份证正面识别失败，请重新上传清晰的身份证正面照片')
        }

      } catch (error) {
        console.error('身份证OCR识别失败:', error)
        throw error
      }
    },

    // 身份证OCR识别（保留原方法用于其他地方调用）
    async recognizeIdCardInfo(tempFilePath) {
      try {
        if (!tempFilePath) {
          uni.showToast({ title: '请先上传身份证正面照片', icon: 'none' })
          return
        }

        uni.showLoading({ title: '识别中...' })

        // 根据角色选择合适的API
        const api = this.getApiByRole()
        const result = await api.recognizeIdCardFront(tempFilePath)

        if (result && this.validateIdCardFrontResult(result)) {
          // 保存OCR识别结果
          this.ocrResults.idCardFront = result

          // 填充识别结果到表单
          this.formData.name = result.name || ''
          this.formData.briefName = result.name || '' // 主体简称默认使用姓名
          this.formData.idNumber = result.id_number || ''
          this.formData.gender = result.gender || ''
          this.formData.birthday = result.birthday || ''

          // 设置性别选择器的索引
          if (this.formData.gender) {
            const genderIndex = this.genderOptions.findIndex(item => item.value === this.formData.gender)
            if (genderIndex !== -1) {
              this.genderIndex = genderIndex
            }
          }

          this.checkAllOcrComplete()
          uni.hideLoading()
          uni.showToast({ title: '身份证正面识别成功', icon: 'success' })
        } else {
          // 识别失败，清空图片
          this.formData.idCardFrontUrl = ''
          this.ocrResults.idCardFront = null
          throw new Error('身份证正面识别失败，请重新上传清晰的身份证正面照片')
        }

      } catch (error) {
        uni.hideLoading()
        console.error('身份证OCR识别失败:', error)

        let errorMsg = '识别失败，请手动填写'
        if (error.message && error.message.includes('网络')) {
          errorMsg = '网络异常，请检查网络连接'
        } else if (error.message) {
          errorMsg = error.message
        }

        uni.showToast({ title: errorMsg, icon: 'none' })
      }
    },

    // 从临时文件进行银行卡OCR识别
    async recognizeBankCardFromTempFile(tempFilePath) {
      try {
        if (!tempFilePath) {
          throw new Error('临时文件路径为空')
        }

        // 根据角色选择合适的API
        const api = this.getApiByRole()
        const result = await api.recognizeBankCard(tempFilePath)

        if (result) {
          // request.js已经处理了code判断，能到这里说明请求成功
          // 填充识别结果到表单
          this.formData.bankName = result.bank_name || ''
          this.formData.accountNo = result.card_number || ''
          this.formData.accountName = result.card_holder || this.formData.name || ''

          return true
        } else {
          throw new Error('识别失败')
        }

      } catch (error) {
        console.error('银行卡OCR识别失败:', error)
        throw error
      }
    },

    // 通过URL进行银行卡OCR识别
    async recognizeBankCardFromUrl(imageUrl) {
      try {
        if (!imageUrl) {
          throw new Error('图片URL为空')
        }

        console.log('开始银行卡OCR识别，URL:', imageUrl)

        // 根据角色选择合适的API
        const api = this.getApiByRole()
        const result = await api.recognizeBankCard(imageUrl)

        console.log('银行卡OCR识别API返回结果:', result)

        if (result && typeof result === 'object') {
          console.log('银行卡OCR识别成功，返回数据')
          return result
        } else {
          console.error('银行卡OCR识别返回数据格式错误:', result)
          throw new Error('识别结果格式错误')
        }
      } catch (error) {
        console.error('银行卡OCR识别失败:', error)
        throw error
      }
    },

    // 银行卡OCR识别（保留原方法用于其他地方调用）
    async recognizeBankCardInfo() {
      try {
        if (!this.bankCardImage) {
          uni.showToast({ title: '请先上传银行卡照片', icon: 'none' })
          return
        }

        uni.showLoading({ title: '识别中...' })

        // 这个方法需要临时文件路径，暂时禁用
        uni.showToast({ title: '请使用上传功能进行识别', icon: 'none' })
        return

        if (result) {
          // request.js已经处理了code判断，能到这里说明请求成功
          // 填充识别结果到表单
          this.formData.bankName = result.bank_name || ''
          this.formData.accountNo = result.card_number || ''
          this.formData.accountName = result.card_holder || this.formData.name || ''

          uni.hideLoading()
          uni.showToast({ title: '银行卡信息识别成功', icon: 'success' })
        } else {
          throw new Error('识别失败')
        }

      } catch (error) {
        uni.hideLoading()
        console.error('银行卡OCR识别失败:', error)

        let errorMsg = '识别失败，请手动填写'
        if (error.message && error.message.includes('网络')) {
          errorMsg = '网络异常，请检查网络连接'
        } else if (error.message) {
          errorMsg = error.message
        }

        uni.showToast({ title: errorMsg, icon: 'none' })
      }
    },

    // 临时文件转File对象
    async tempFileToFile(tempFilePath, filename) {
      return new Promise((resolve, reject) => {
        // 在小程序环境中，直接使用临时文件路径
        // #ifdef MP-WEIXIN
        // 小程序环境下，我们需要创建一个类似File的对象
        const fileObj = {
          name: filename,
          type: 'image/jpeg',
          path: tempFilePath,
          // 添加一个标识，表示这是小程序的临时文件
          isMiniProgramFile: true
        }
        resolve(fileObj)
        // #endif

        // #ifdef H5
        // H5环境下使用标准的File API
        fetch(tempFilePath)
          .then(response => response.blob())
          .then(blob => {
            const file = new File([blob], filename, { type: 'image/jpeg' })
            resolve(file)
          })
          .catch(reject)
        // #endif
      })
    },

    // base64转File对象
    base64ToFile(base64Data, filename) {
      // 移除data:image/jpeg;base64,前缀
      const base64 = base64Data.replace(/^data:image\/[a-z]+;base64,/, '')

      // 将base64转换为二进制数据
      const byteCharacters = atob(base64)
      const byteNumbers = new Array(byteCharacters.length)

      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i)
      }

      const byteArray = new Uint8Array(byteNumbers)

      // 创建File对象
      return new File([byteArray], filename, { type: 'image/jpeg' })
    },

    // 表单验证
    validateForm() {
      // 验证OCR识别结果
      if (!this.ocrResults.idCardFront) {
        uni.showToast({ title: '请上传并识别身份证正面', icon: 'none' })
        return false
      }

      if (!this.ocrResults.idCardBack) {
        uni.showToast({ title: '请上传并识别身份证背面', icon: 'none' })
        return false
      }

      if (!this.ocrResults.bankCard) {
        uni.showToast({ title: '请上传并识别银行卡照片', icon: 'none' })
        return false
      }

      return true
    },

    // 验证身份证正面OCR结果
    validateIdCardFrontResult(result) {
      // 检查必要字段是否存在且有效
      if (!result) return false

      console.log('身份证正面OCR识别结果:', result)

      const hasName = result.name && result.name.trim().length >= 2
      const hasIdNumber = result.id_number && result.id_number.trim().length >= 15

      console.log('身份证正面验证结果:', { hasName, hasIdNumber, name: result.name, id_number: result.id_number })

      // 只要有姓名或身份证号其中之一就认为识别成功
      return hasName || hasIdNumber
    },

    // 验证身份证背面OCR结果
    validateIdCardBackResult(result) {
      // 检查必要字段是否存在且有效
      if (!result) return false

      console.log('身份证背面OCR识别结果:', result)

      // 兼容不同的字段名
      const authority = result.issuing_authority || result.issue_authority || ''
      const validity = result.validity_period || result.valid_period || ''

      const hasAuthority = authority && authority.trim().length > 0
      const hasValidity = validity && validity.trim().length > 0

      console.log('身份证背面验证结果:', { hasAuthority, hasValidity, authority, validity })

      // 只要有签发机关或有效期其中之一就认为识别成功
      return hasAuthority || hasValidity
    },

    // 验证银行卡OCR结果
    validateBankCardResult(result) {
      // 检查必要字段是否存在且有效
      if (!result) {
        console.log('银行卡OCR结果为空')
        return false
      }

      console.log('银行卡OCR识别结果:', result)
      console.log('银行卡OCR结果类型:', typeof result)
      console.log('银行卡OCR结果的所有键:', Object.keys(result))

      // 如果result有data字段，可能需要使用result.data
      const actualData = result.data || result

      console.log('实际使用的数据:', actualData)
      console.log('实际数据类型:', typeof actualData)
      console.log('实际数据的所有键:', actualData && typeof actualData === 'object' ? Object.keys(actualData) : 'not object')

      // 检查卡号（可能的字段名：card_number, cardNumber, number）
      const cardNumber = actualData.card_number || actualData.cardNumber || actualData.number || ''
      // 银行卡号通常16-19位，允许有空格
      const cleanCardNumber = cardNumber.replace(/\s/g, '')
      const hasCardNumber = cleanCardNumber && /^\d{16,19}$/.test(cleanCardNumber)

      // 检查银行名称（可能的字段名：bank_name, bankName, bank）
      const bankName = actualData.bank_name || actualData.bankName || actualData.bank || ''
      const hasBankName = bankName && bankName.trim().length > 0

      console.log('银行卡验证结果:', {
        hasCardNumber,
        hasBankName,
        cardNumber,
        cleanCardNumber,
        bankName,
        cardNumberLength: cleanCardNumber.length
      })

      // 只要有卡号或银行名称其中之一就认为识别成功
      const isValid = hasCardNumber || hasBankName
      console.log('银行卡最终验证结果:', isValid)

      return isValid
    },

    // 检查所有OCR是否完成
    checkAllOcrComplete() {
      const allComplete = this.ocrResults.idCardFront &&
                         this.ocrResults.idCardBack &&
                         this.ocrResults.bankCard

      if (allComplete) {
        this.ocrRecognized = true
        console.log('所有OCR识别完成，可以提交申请')
      }
    },

    // 身份证背面OCR识别
    async recognizeIdCardBackFromTempFile(tempFilePath) {
      try {
        const api = this.getApiByRole()
        const result = await api.recognizeIdCardBack(tempFilePath)
        return result
      } catch (error) {
        console.error('身份证背面OCR识别失败:', error)
        return null
      }
    },

    // 提交申请
    async submitApplication() {
      try {
        // 验证表单
        if (!this.validateForm()) {
          return
        }

        this.submitting = true
        uni.showLoading({ title: '提交中...' })

        // 提交手机号、OCR识别结果和图片URL
        const submitData = {
          mobile: this.formData.mobile,
          // OCR识别结果
          id_card_front_ocr: this.ocrResults.idCardFront || null,
          id_card_back_ocr: this.ocrResults.idCardBack || null,
          bank_card_ocr: this.ocrResults.bankCard || null,
          // 图片URL
          id_card_front_url: this.formData.idCardFrontUrl || null,
          id_card_back_url: this.formData.idCardBackUrl || null,
          bank_card_url: this.formData.bankCardUrl || null
        }

        // 打印OCR结果和图片URL日志
        if (this.ocrResults.idCardFront) {
          console.log('添加身份证正面OCR结果:', this.ocrResults.idCardFront)
        }
        if (this.ocrResults.idCardBack) {
          console.log('添加身份证背面OCR结果:', this.ocrResults.idCardBack)
        }
        if (this.ocrResults.bankCard) {
          console.log('添加银行卡OCR结果:', this.ocrResults.bankCard)
        }

        // 打印图片URL日志
        console.log('图片URL信息:', {
          idCardFrontUrl: this.formData.idCardFrontUrl,
          idCardBackUrl: this.formData.idCardBackUrl,
          bankCardUrl: this.formData.bankCardUrl
        })

        console.log('完整提交数据:', submitData)

        // 根据角色选择合适的API
        const api = this.getApiByRole()
        const result = await api.submitApplication(submitData)

        uni.hideLoading()


          uni.showToast({ title: '提交成功', icon: 'success' })

          // 跳转到结果页面或返回上一页
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)

      } catch (error) {
        uni.hideLoading()
        console.error('提交开户申请失败:', error)
        uni.showToast({ title: '提交失败', icon: 'error' })
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.application-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 20rpx;
}

.section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  position: relative;
  padding-left: 20rpx;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6rpx;
    height: 32rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 3rpx;
  }
}

.tips-box {
  background: #f8f9ff;
  border: 2rpx solid #e6f0ff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.tips-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 选择器样式 */
.picker {
  width: 100%;
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 32rpx;
  color: #333;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    right: 24rpx;
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
    width: 12rpx;
    height: 12rpx;
    border-right: 2rpx solid #999;
    border-bottom: 2rpx solid #999;
  }
}

.form-item {
  margin-bottom: 30rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.required {
  color: #ff4757;
  margin-left: 4rpx;
}

.input {
  width: 100%;
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;

  &:focus {
    border-color: #667eea;
    background: white;
  }

  &:disabled {
    background: #f1f3f4;
    color: #999;
  }
}

.picker {
  width: 100%;
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    right: 24rpx;
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
    width: 12rpx;
    height: 12rpx;
    border-right: 2rpx solid #999;
    border-bottom: 2rpx solid #999;
  }
}

.upload-area {
  width: 100%;
  height: 200rpx;
  border: 2rpx dashed #d1d5db;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  position: relative;
  overflow: hidden;

  &:active {
    background: #f0f0f0;
  }
}

.upload-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10rpx;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.upload-text {
  font-size: 26rpx;
  color: #999;
}

.submit-section {
  padding: 40rpx 20rpx;
  background: white;
  margin-top: 20rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 44rpx;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;

  &.disabled {
    background: #d1d5db;
    color: #9ca3af;
  }

  &:not(.disabled):active {
    transform: scale(0.98);
    transition: transform 0.1s;
  }
}
</style>
