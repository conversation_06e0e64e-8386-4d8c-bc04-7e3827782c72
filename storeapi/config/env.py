import argparse
import os
import sys
from dotenv import load_dotenv
from functools import lru_cache
from pydantic import computed_field
from pydantic_settings import BaseSettings
from typing import Literal


class AppSettings(BaseSettings):
    """
    应用配置
    """

    app_env: str = 'dev'
    app_name: str = 'RuoYi-FastAPI'
    app_root_path: str = ''
    app_host: str = '0.0.0.0'
    app_port: int = 9099
    app_version: str = '1.0.0'
    app_reload: bool = True
    app_ip_location_query: bool = True
    app_same_time_login: bool = True
    # Swagger文档认证配置
    swagger_username: str = "admin"
    swagger_password: str = "admin123"


class JwtSettings(BaseSettings):
    """
    Jwt配置
    """

    jwt_secret_key: str = 'b01c66dc2c58dc6a0aabfe2144256be36226de378bf87f72c0c795dda67f4d55'
    jwt_algorithm: str = 'HS256'
    jwt_expire_minutes: int = 43200  # 一个月 = 30天 * 24小时 * 60分钟
    jwt_redis_expire_minutes: int = 43200  # 一个月 = 43200分钟


class DataBaseSettings(BaseSettings):
    """
    数据库配置
    """

    db_type: Literal['mysql', 'postgresql'] = 'mysql'
    db_host: str = '127.0.0.1'
    db_port: int = 3306
    db_username: str = 'root'
    db_password: str = 'mysqlroot'
    db_database: str = 'ruoyi-fastapi'
    db_echo: bool = True
    db_max_overflow: int = 10
    db_pool_size: int = 50
    db_pool_recycle: int = 3600
    db_pool_timeout: int = 30

    @computed_field
    @property
    def sqlglot_parse_dialect(self) -> str:
        if self.db_type == 'postgresql':
            return 'postgres'
        return self.db_type


class RedisSettings(BaseSettings):
    """
    Redis配置
    """

    redis_host: str = '127.0.0.1'
    redis_port: int = 6379
    redis_username: str = ''
    redis_password: str = ''
    redis_database: int = 2


class GenSettings:
    """
    代码生成配置
    """

    author = 'insistence'
    package_name = 'module_admin.system'
    auto_remove_pre = False
    table_prefix = 'sys_'
    allow_overwrite = False

    GEN_PATH = 'vf_admin/gen_path'

    def __init__(self):
        if not os.path.exists(self.GEN_PATH):
            os.makedirs(self.GEN_PATH)


class UploadSettings:
    """
    上传配置
    """

    UPLOAD_PREFIX = '/profile'
    UPLOAD_PATH = 'vf_admin/upload_path'
    UPLOAD_MACHINE = 'A'
    DEFAULT_ALLOWED_EXTENSION = [
        # 图片
        'bmp',
        'gif',
        'jpg',
        'jpeg',
        'png',
        # word excel powerpoint
        'doc',
        'docx',
        'xls',
        'xlsx',
        'ppt',
        'pptx',
        'html',
        'htm',
        'txt',
        # 压缩文件
        'rar',
        'zip',
        'gz',
        'bz2',
        # 视频格式
        'mp4',
        'avi',
        'rmvb',
        # pdf
        'pdf',
    ]
    DOWNLOAD_PATH = 'vf_admin/download_path'

    def __init__(self):
        if not os.path.exists(self.UPLOAD_PATH):
            os.makedirs(self.UPLOAD_PATH)
        if not os.path.exists(self.DOWNLOAD_PATH):
            os.makedirs(self.DOWNLOAD_PATH)


class ObsSettings(BaseSettings):
    """
    华为云OBS配置
    """

    obs_access_key: str = ''
    obs_secret_key: str = ''
    obs_bucket: str = ''
    obs_endpoint: str = ''
    obs_region: str = ''
    obs_url_prefix: str = ''
    obs_directory: str = ''


class AliyunSmsSettings(BaseSettings):
    """
    阿里云短信配置
    """

    sms_access_key_id: str = 'LTAIfTID1WDWpmTL'
    sms_access_key_secret: str = 'S7SQEdtiAcuZDTK4ATYSTooSfrVs4H'
    sms_sign_name: str = '小羽佳高端家政'
    sms_api_url: str = 'https://dysmsapi.aliyuncs.com'
    sms_verification_template_code: str = 'SMS_485335604'  # 验证码短信模板ID
    sms_expire_minutes: int = 5  # 短信验证码有效期（分钟）


class BaiduVoiceSettings(BaseSettings):
    """
    百度语音识别配置
    """

    api_key: str = ''  # 百度语音识别API Key
    secret_key: str = ''  # 百度语音识别Secret Key

    class Config:
        env_prefix = 'BAIDU_VOICE_'


class BaiduOcrSettings(BaseSettings):
    """
    百度OCR识别配置
    """

    app_id: str = ''  # 百度OCR App ID
    api_key: str = ''  # 百度OCR API Key
    secret_key: str = ''  # 百度OCR Secret Key

    class Config:
        env_prefix = 'BAIDU_OCR_'


class WechatSettings(BaseSettings):
    """
    微信小程序和公众号配置
    """

    # 微信小程序配置
    wechat_app_id: str = 'wx3aa06d66a59ba971'  # 微信小程序AppID
    wechat_app_secret: str  # 微信小程序AppSecret，从环境变量读取
    wechat_api_url: str = 'https://api.weixin.qq.com'  # 微信API基础URL

    # 微信公众号配置
    wechat_official_app_id: str = ''  # 微信公众号AppID，从环境变量读取
    wechat_official_app_secret: str = ''  # 微信公众号AppSecret，从环境变量读取
    wechat_official_token: str = 'your_wechat_token'  # 微信公众号Token，从环境变量读取
    wechat_official_encoding_aes_key: str = ''  # 微信公众号EncodingAESKey，从环境变量读取


class YeepaySettings(BaseSettings):
    """
    易宝支付配置
    """

    # 易宝支付API配置
    YEEPAY_API_URL: str = 'https://api.kuaijie-pay.com/forward/pay/txn/v2/wxpay/pay/miniapp'
    YEEPAY_CUSTOMER_CODE: str = 'C0000001C0001'  # 客户代码，需要替换为实际值
    YEEPAY_VERSION: str = '2.0'
    YEEPAY_TIMEOUT: int = 30  # 请求超时时间（秒）

    # RSA密钥配置
    YEEPAY_MERCHANT_PRIVATE_KEY: str = ''  # 商户私钥（PKCS1格式，不含头尾）
    YEEPAY_PLATFORM_PUBLIC_KEY: str = ''   # 平台公钥（不含头尾）

    class Config:
        # 不使用前缀，直接使用完整的环境变量名
        pass


class JizhengSettings(BaseSettings):
    """
    极证云信用查询配置
    """

    # 3DES密钥 - 需要从极证云平台获取
    jizheng_des_key: str = 'z6G9IwbkNXvR1arQBpRFqCYk'

    # 商户号 - 需要从极证云平台获取
    jizheng_customer_code: str = '1015535067158314'

    # API配置
    jizheng_api_base_url: str = 'https://api.jizhengyun.com'
    jizheng_api_timeout: int = 30

    # RSA私钥 - 从rsa私钥.txt文件获取
    jizheng_private_key: str = '''***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'''

    # RSA公钥 - 极证云系统公钥，用于验证响应签名（从系统公钥.txt文件获取）
    jizheng_public_key: str = '''-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDLMO5uvGzV4GwQwyOkS+H5ZIbE
NbAXTNMr8QfQ1wTSS7mSAua2q3SmhqUg/fduks26OnQGy9JRG3x9uiqwXb7/b7P4
uX5lYHhs4olSp85tgTGBYY93bE24VjxCaPi7NuegvilnR+cRYIzeHm3klT7iYjr1
5i+HSxGSaYTz1bQoxwIDAQAB
-----END PUBLIC KEY-----'''


class CachePathConfig:
    """
    缓存目录配置
    """

    PATH = os.path.join(os.path.abspath(os.getcwd()), 'caches')
    PATHSTR = 'caches'


class GetConfig:
    """
    获取配置
    """

    def __init__(self):
        self.parse_cli_args()

    @lru_cache()
    def get_app_config(self):
        """
        获取应用配置
        """
        # 实例化应用配置模型
        return AppSettings()

    @lru_cache()
    def get_jwt_config(self):
        """
        获取Jwt配置
        """
        # 实例化Jwt配置模型
        return JwtSettings()

    @lru_cache()
    def get_database_config(self):
        """
        获取数据库配置
        """
        # 实例化数据库配置模型
        return DataBaseSettings()

    @lru_cache()
    def get_redis_config(self):
        """
        获取Redis配置
        """
        # 实例化Redis配置模型
        return RedisSettings()

    @lru_cache()
    def get_gen_config(self):
        """
        获取代码生成配置
        """
        # 实例化代码生成配置
        return GenSettings()

    @lru_cache()
    def get_upload_config(self):
        """
        获取上传配置
        """
        # 实例上传配置
        return UploadSettings()

    @lru_cache()
    def get_obs_config(self):
        """
        获取华为云OBS配置
        """
        # 实例化华为云OBS配置模型
        return ObsSettings()

    @lru_cache()
    def get_aliyun_sms_config(self):
        """
        获取阿里云短信配置
        """
        # 实例化阿里云短信配置模型
        return AliyunSmsSettings()

    @lru_cache()
    def get_wechat_config(self):
        """
        获取微信小程序配置
        """
        # 实例化微信配置模型
        return WechatSettings()

    @lru_cache()
    def get_baidu_voice_config(self):
        """
        获取百度语音识别配置
        """
        # 实例化百度语音识别配置模型
        return BaiduVoiceSettings()

    @lru_cache()
    def get_baidu_ocr_config(self):
        """
        获取百度OCR识别配置
        """
        # 实例化百度OCR识别配置模型
        return BaiduOcrSettings()

    @lru_cache()
    def get_yeepay_config(self):
        """
        获取易宝支付配置
        """
        # 实例化易宝支付配置模型
        return YeepaySettings()

    @lru_cache()
    def get_jizheng_config(self):
        """
        获取极证云信用查询配置
        """
        # 实例化极证云配置模型
        return JizhengSettings()

    @staticmethod
    def parse_cli_args():
        """
        解析命令行参数
        """
        if 'uvicorn' in sys.argv[0]:
            # 使用uvicorn启动时，命令行参数需要按照uvicorn的文档进行配置，无法自定义参数
            pass
        else:
            # 使用argparse定义命令行参数
            parser = argparse.ArgumentParser(description='命令行参数')
            parser.add_argument('--env', type=str, default='', help='运行环境')
            # 解析命令行参数
            args = parser.parse_args()
            # 设置环境变量，如果未设置命令行参数，默认APP_ENV为dev
            os.environ['APP_ENV'] = args.env if args.env else 'dev'
        # 读取运行环境
        run_env = os.environ.get('APP_ENV', '')
        # 运行环境未指定时默认加载.env.dev
        env_file = '.env.dev'
        # 运行环境不为空时按命令行参数加载对应.env文件
        if run_env != '':
            env_file = f'.env.{run_env}'
        # 加载配置
        load_dotenv(env_file)


# 实例化获取配置类
get_config = GetConfig()
# 应用配置
AppConfig = get_config.get_app_config()
# Jwt配置
JwtConfig = get_config.get_jwt_config()
# 数据库配置
DataBaseConfig = get_config.get_database_config()
# Redis配置
RedisConfig = get_config.get_redis_config()
# 代码生成配置
GenConfig = get_config.get_gen_config()
# 上传配置
UploadConfig = get_config.get_upload_config()
# 华为云OBS配置
ObsConfig = get_config.get_obs_config()
# 阿里云短信配置
AliyunSmsConfig = get_config.get_aliyun_sms_config()
# 微信小程序配置
WechatConfig = get_config.get_wechat_config()
# 百度语音识别配置
BaiduVoiceConfig = get_config.get_baidu_voice_config()
# 百度OCR识别配置
BaiduOcrConfig = get_config.get_baidu_ocr_config()
# 易宝支付配置
YeepayConfig = get_config.get_yeepay_config()
# 极证云信用查询配置
JizhengConfig = get_config.get_jizheng_config()
