import { post, get } from '../utlis/require.js'
import { host } from '../setConfig.js'

/**
 * 开户申请相关API
 */

// 提交开户申请
export const submitAccountApplication = (data) => {
  return post('/api/v1/account/application/submit', data, { contentType: 'application/json' })
}

// 查询开户申请状态
export const getApplicationStatus = (mobile) => {
  return get('/api/v1/account/application/status', { mobile })
}

// 获取开户申请详情
export const getApplicationDetail = (uuid) => {
  return get('/api/v1/account/application/detail', { uuid })
}

// 获取开户申请列表（管理员用）
export const getApplicationList = (params = {}) => {
  return get('/api/v1/account/application/list', params)
}

// 审核开户申请（管理员用）
export const reviewApplication = (data) => {
  return post('/api/v1/account/application/review', data, { contentType: 'application/json' })
}

// OCR识别身份证正面（通过图片URL）
export const recognizeIdCardFront = (imageUrl) => {
  return post('/api/v1/ocr/idcard/front', { imageUrl }, { contentType: 'application/json' })
}

// OCR识别身份证背面（通过图片URL）
export const recognizeIdCardBack = (imageUrl) => {
  return post('/api/v1/ocr/idcard/back', { imageUrl }, { contentType: 'application/json' })
}

// OCR识别银行卡（通过图片URL）
export const recognizeBankCard = async (imageUrl) => {
  try {
    // 使用原始的post方法，但不依赖request.js的数据处理
    const result = await post('/api/v1/ocr/bankcard', { imageUrl }, { contentType: 'application/json', showErr: false })
    console.log('银行卡OCR API原始返回:', result)

    // 如果result是boolean类型，说明request.js处理有问题
    if (typeof result === 'boolean') {
      console.warn('银行卡OCR返回了boolean值，可能是数据处理问题')
      // 尝试直接调用原始API
      return await callBankCardOCRDirectly(imageUrl)
    }

    return result
  } catch (error) {
    console.error('银行卡OCR识别失败:', error)
    // 如果失败，尝试直接调用
    try {
      return await callBankCardOCRDirectly(imageUrl)
    } catch (directError) {
      console.error('直接调用银行卡OCR也失败:', directError)
      throw error
    }
  }
}

// 直接调用银行卡OCR API的备用方法
async function callBankCardOCRDirectly(imageUrl) {
  return new Promise((resolve, reject) => {
    uni.request({
      url: `${host}/api/v1/ocr/bankcard`,
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${uni.getStorageSync('token')}`
      },
      data: { imageUrl },
      success: (res) => {
        console.log('银行卡OCR直接调用原始返回:', res)
        if (res.statusCode === 200 && res.data.code === 200) {
          console.log('银行卡OCR直接调用成功，数据:', res.data.data)
          resolve(res.data.data)
        } else {
          reject(new Error(res.data.msg || '识别失败'))
        }
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}

// OCR识别营业执照（通过图片URL）
export const recognizeBusinessLicense = (imageUrl) => {
  return post('/api/v1/ocr/business-license', { imageUrl }, { contentType: 'application/json' })
}

// 从临时文件路径进行身份证正面OCR识别
export const recognizeIdCardFrontFromTempFile = async (tempFilePath) => {
  // 先上传文件获取URL
  const uploadResult = await uploadFile(tempFilePath)
  if (uploadResult && uploadResult.success && uploadResult.data && uploadResult.data.url) {
    // 使用URL进行OCR识别
    return recognizeIdCardFront(uploadResult.data.url)
  } else {
    throw new Error('文件上传失败')
  }
}

// 从临时文件路径进行身份证背面OCR识别
export const recognizeIdCardBackFromTempFile = async (tempFilePath) => {
  // 先上传文件获取URL
  const uploadResult = await uploadFile(tempFilePath)
  if (uploadResult && uploadResult.success && uploadResult.data && uploadResult.data.url) {
    // 使用URL进行OCR识别
    return recognizeIdCardBack(uploadResult.data.url)
  } else {
    throw new Error('文件上传失败')
  }
}

// 从临时文件路径进行银行卡OCR识别
export const recognizeBankCardFromTempFile = async (tempFilePath) => {
  try {
    console.log('开始从临时文件进行银行卡OCR识别:', tempFilePath)

    // 先上传文件获取URL
    const uploadResult = await uploadFile(tempFilePath)
    console.log('银行卡文件上传结果:', uploadResult)

    if (uploadResult && uploadResult.success && uploadResult.data && uploadResult.data.url) {
      // 使用URL进行OCR识别
      console.log('开始调用银行卡OCR识别，URL:', uploadResult.data.url)
      const ocrResult = await recognizeBankCard(uploadResult.data.url)
      console.log('银行卡OCR识别完成，结果:', ocrResult)
      return ocrResult
    } else {
      console.error('银行卡文件上传失败:', uploadResult)
      throw new Error('文件上传失败')
    }
  } catch (error) {
    console.error('银行卡OCR识别过程出错:', error)
    throw error
  }
}

// 从临时文件路径进行营业执照OCR识别
export const recognizeBusinessLicenseFromTempFile = async (tempFilePath) => {
  // 先上传文件获取URL
  const uploadResult = await uploadFile(tempFilePath)
  if (uploadResult && uploadResult.success && uploadResult.data && uploadResult.data.url) {
    // 使用URL进行OCR识别
    return recognizeBusinessLicense(uploadResult.data.url)
  } else {
    throw new Error('文件上传失败')
  }
}

// 通用文件上传方法
const uploadFile = (tempFilePath) => {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: host + '/api/v1/public/upload',
      filePath: tempFilePath,
      name: 'file',
      header: {
        'Authorization': 'Bearer ' + uni.getStorageSync('token')
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data)
          resolve(data)
        } catch (error) {
          reject(new Error('响应解析失败'))
        }
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}
