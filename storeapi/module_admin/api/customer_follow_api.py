"""
客户跟进API接口
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_admin.service.customer_follow_service import CustomerFollowService
from utils.response_util import ResponseUtil
from utils.log_util import logger
from pydantic import BaseModel
from typing import Optional, List, Dict, Any

router = APIRouter(prefix="/customer-follow", tags=["客户跟进"])


class FileInfoModel(BaseModel):
    """文件信息模型"""
    fileName: str
    fileType: str
    fileSize: int
    url: str
    originalFilename: Optional[str] = None
    newFileName: Optional[str] = None


class CreateFollowRequest(BaseModel):
    """创建跟进记录请求"""
    customer_uuid: str
    follow_content: str
    next_follow_time: Optional[str] = None
    files: Optional[List[FileInfoModel]] = []


class UpdateFollowRequest(BaseModel):
    """更新跟进记录请求"""
    follow_uuid: str
    follow_content: str
    next_follow_time: Optional[str] = None
    files: Optional[List[FileInfoModel]] = []


class UpdateStatusWithFollowRequest(BaseModel):
    """更新状态并跟进请求"""
    customer_id: str
    status: str
    follow_content: Optional[str] = None
    next_follow_time: Optional[str] = None
    files: Optional[List[FileInfoModel]] = []


@router.post("/create")
async def create_follow_record(
    request: CreateFollowRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    创建客户跟进记录（支持图片上传）
    """
    try:
        # 转换文件信息为字典格式
        files_data = []
        if request.files:
            for file_info in request.files:
                files_data.append({
                    "fileName": file_info.fileName,
                    "fileType": file_info.fileType,
                    "fileSize": file_info.fileSize,
                    "url": file_info.url,
                    "originalFilename": file_info.originalFilename,
                    "newFileName": file_info.newFileName
                })

        result = await CustomerFollowService.create_follow_record_with_files(
            db=db,
            customer_uuid=request.customer_uuid,
            follow_content=request.follow_content,
            next_follow_time=request.next_follow_time,
            files=files_data
        )

        return ResponseUtil.success(data=result, msg="跟进记录创建成功")

    except Exception as e:
        logger.error(f"创建跟进记录失败: {str(e)}")
        return ResponseUtil.error(msg=f"创建跟进记录失败: {str(e)}")


@router.put("/update")
async def update_follow_record(
    request: UpdateFollowRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    更新客户跟进记录（支持图片上传）
    """
    try:
        # 转换文件信息为字典格式
        files_data = []
        if request.files:
            for file_info in request.files:
                files_data.append({
                    "fileName": file_info.fileName,
                    "fileType": file_info.fileType,
                    "fileSize": file_info.fileSize,
                    "url": file_info.url,
                    "originalFilename": file_info.originalFilename,
                    "newFileName": file_info.newFileName
                })

        result = await CustomerFollowService.update_follow_record_with_files(
            db=db,
            follow_uuid=request.follow_uuid,
            follow_content=request.follow_content,
            next_follow_time=request.next_follow_time,
            files=files_data
        )

        return ResponseUtil.success(data=result, msg="跟进记录更新成功")

    except Exception as e:
        logger.error(f"更新跟进记录失败: {str(e)}")
        return ResponseUtil.error(msg=f"更新跟进记录失败: {str(e)}")


@router.get("/detail/{follow_uuid}")
async def get_follow_record_detail(
    follow_uuid: str,
    db: AsyncSession = Depends(get_db)
):
    """
    获取单个跟进记录详情（包含图片）
    """
    try:
        result = await CustomerFollowService.get_follow_record_detail(
            db=db,
            follow_uuid=follow_uuid
        )

        return ResponseUtil.success(data=result["data"], msg="获取跟进记录详情成功")

    except Exception as e:
        logger.error(f"获取跟进记录详情失败: {str(e)}")
        return ResponseUtil.error(msg=f"获取跟进记录详情失败: {str(e)}")


@router.get("/list")
async def get_customer_follow_list(
    customer_uuid: str = Query(..., description="客户UUID"),
    page: int = Query(1, description="页码"),
    size: int = Query(10, description="每页数量"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取客户跟进记录列表
    """
    try:
        result = await CustomerFollowService.get_customer_follow_list(
            db=db,
            customer_uuid=customer_uuid,
            page=page,
            size=size
        )
        
        return ResponseUtil.success(data=result["data"], msg="获取跟进记录列表成功")

    except Exception as e:
        logger.error(f"获取跟进记录列表失败: {str(e)}")
        return ResponseUtil.error(msg=f"获取跟进记录列表失败: {str(e)}")


@router.post("/update-status")
async def update_customer_status_with_follow(
    request: UpdateStatusWithFollowRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    更新客户状态并创建跟进记录（支持图片上传）
    """
    try:
        # 转换文件信息为字典格式
        files_data = []
        if request.files:
            for file_info in request.files:
                files_data.append({
                    "fileName": file_info.fileName,
                    "fileType": file_info.fileType,
                    "fileSize": file_info.fileSize,
                    "url": file_info.url,
                    "originalFilename": file_info.originalFilename,
                    "newFileName": file_info.newFileName
                })

        result = await CustomerFollowService.update_customer_status_with_follow_and_files(
            db=db,
            customer_id=request.customer_id,
            status=request.status,
            follow_content=request.follow_content,
            next_follow_time=request.next_follow_time,
            files=files_data
        )

        return ResponseUtil.success(data=result, msg="客户状态更新成功")

    except Exception as e:
        logger.error(f"更新客户状态失败: {str(e)}")
        return ResponseUtil.error(msg=f"更新客户状态失败: {str(e)}")


@router.get("/latest")
async def get_latest_follow_record(
    customer_uuid: str = Query(..., description="客户UUID"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取客户最新的跟进记录
    """
    try:
        result = await CustomerFollowService.get_latest_follow_record(
            db=db,
            customer_uuid=customer_uuid
        )
        
        return ResponseUtil.success(data=result["data"], msg="获取最新跟进记录成功")

    except Exception as e:
        logger.error(f"获取最新跟进记录失败: {str(e)}")
        return ResponseUtil.error(msg=f"获取最新跟进记录失败: {str(e)}")
