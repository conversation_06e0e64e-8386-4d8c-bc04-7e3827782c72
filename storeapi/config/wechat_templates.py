"""
微信公众号模板消息配置
"""
from enum import Enum
from typing import Dict, Any, Optional
from dataclasses import dataclass


class TemplateType(Enum):
    """微信模板消息类型枚举"""
    ORDER_NOTIFICATION = "ORDER_NOTIFICATION"
    DISPATCH_NOTIFICATION = "DISPATCH_NOTIFICATION"
    DISPATCH_SIMPLE_NOTIFICATION = "DISPATCH_SIMPLE_NOTIFICATION"
    BIND_SUCCESS_NOTIFICATION = "BIND_SUCCESS_NOTIFICATION"
    STAFF_BIND_SUCCESS_NOTIFICATION = "STAFF_BIND_SUCCESS_NOTIFICATION"


@dataclass
class MiniprogramConfig:
    """小程序跳转配置"""
    appid: str
    pagepath: str


@dataclass
class TemplateConfig:
    """微信模板配置"""
    template_id: str
    template_code: str
    name: str
    category: str
    description: str
    fields: Dict[str, str]
    miniprogram: Optional[MiniprogramConfig] = None


class WechatTemplates:
    """微信公众号模板消息配置管理"""
    
    # 默认小程序AppID（从配置中获取）
    DEFAULT_MINIPROGRAM_APPID = 'wx3aa06d66a59ba971'
    
    # 模板配置字典
    TEMPLATES: Dict[TemplateType, TemplateConfig] = {
        # 家政服务订单通知
        TemplateType.ORDER_NOTIFICATION: TemplateConfig(
            template_id='NMd-A5NdR63uTx2UBFBnsRz51L_awuhlXl9uq7l7aaI',
            template_code='44645',
            name='家政服务订单通知',
            category='家政服务',
            description='新增订单推送定时任务',
            fields={
                'order_number': 'character_string27',  # 订单编号
                'service_time': 'time5',              # 下单时间
                'service_name': 'thing2',             # 服务名称
                'order_status': 'short_thing4'        # 订单状态
            },
            miniprogram=MiniprogramConfig(
                appid=DEFAULT_MINIPROGRAM_APPID,
                pagepath='pages/dispatch/index'
            )
        ),
        
        # 需求单派单提醒
        TemplateType.DISPATCH_NOTIFICATION: TemplateConfig(
            template_id='TlLuvu_yM_I1uG2AIg5QcgShB_kuJoFld6yf8-Vc9R4',
            template_code='49279',
            name='需求单派单提醒',
            category='家政服务',
            description='员工在接到派单之后的通知',
            fields={
                'demand_number': 'character_string1',  # 需求单号
                'service_store': 'thing2',            # 服务门店
                'service_type': 'thing3',             # 服务类目
                'service_address': 'thing5',          # 服务地址
                'appointment_time': 'time6'           # 上门时间
            },
            miniprogram=MiniprogramConfig(
                appid=DEFAULT_MINIPROGRAM_APPID,
                pagepath='pages-staff/home/<USER>'
            )
        ),
        
        # 需求单派单提醒（简化版）
        TemplateType.DISPATCH_SIMPLE_NOTIFICATION: TemplateConfig(
            template_id='TlLuvu_yM_l1uG2Alg5QctQNjk_lngT4Lj4HUqyF3Tg',
            template_code='49279',
            name='需求单派单提醒',
            category='家政服务',
            description='客户下线索后提醒店长',
            fields={
                'demand_number': 'character_string1',  # 需求单号
                'service_type': 'thing3',             # 服务类目
                'service_address': 'thing5'           # 服务地址
            },
            miniprogram=MiniprogramConfig(
                appid=DEFAULT_MINIPROGRAM_APPID,
                pagepath='pages/dispatch/list'
            )
        ),
        
        # 用户绑定成功通知
        TemplateType.BIND_SUCCESS_NOTIFICATION: TemplateConfig(
            template_id='zJmewq8-YByVMm4s_AfwKb3ZsAZDEDD-A4UMx1EMfiw',
            template_code='60720',
            name='用户绑定成功通知',
            category='软件/建站/技术开发',
            description='用于推送用户成功绑定公众号推送',
            fields={
                'user_name': 'thing1',     # 用户姓名
                'platform_name': 'thing2'  # 平台名称
            },
            miniprogram=MiniprogramConfig(
                appid=DEFAULT_MINIPROGRAM_APPID,
                pagepath='pages/home/<USER>'
            )
        ),

        # 员工绑定成功通知
        TemplateType.STAFF_BIND_SUCCESS_NOTIFICATION: TemplateConfig(
            template_id='zJmewq8-YByVMm4s_AfwKb3ZsAZDEDD-A4UMx1EMfiw',
            template_code='60720',
            name='员工绑定成功通知',
            category='软件/建站/技术开发',
            description='用于推送员工成功绑定公众号推送',
            fields={
                'user_name': 'thing1',     # 员工姓名
                'platform_name': 'thing2'  # 平台名称
            },
            miniprogram=MiniprogramConfig(
                appid=DEFAULT_MINIPROGRAM_APPID,
                pagepath='pages-staff/home/<USER>'
            )
        )
    }
    
    @classmethod
    def get_template_config(cls, template_type: TemplateType) -> Optional[TemplateConfig]:
        """获取模板配置"""
        return cls.TEMPLATES.get(template_type)
    
    @classmethod
    def build_template_data(cls, template_type: TemplateType, data: Dict[str, Any]) -> Dict[str, Any]:
        """构建模板消息数据"""
        config = cls.get_template_config(template_type)
        if not config:
            raise ValueError(f"未找到模板类型: {template_type}")
        
        template_data = {}
        
        # 根据配置的字段映射构建数据
        for key, field_name in config.fields.items():
            if key in data and data[key] is not None:
                template_data[field_name] = {
                    "value": str(data[key])
                }
        
        return template_data
    
    @classmethod
    def get_miniprogram_config(cls, template_type: TemplateType, custom_appid: Optional[str] = None) -> Optional[Dict[str, str]]:
        """获取小程序跳转配置"""
        config = cls.get_template_config(template_type)
        if not config or not config.miniprogram:
            return None
        
        return {
            'appid': custom_appid or config.miniprogram.appid,
            'pagepath': config.miniprogram.pagepath
        }
