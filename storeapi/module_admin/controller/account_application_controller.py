from fastapi import APIRouter, Depends, Query, UploadFile, File, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
import json

from module_admin.service.account_application_service import AccountApplicationService
from module_admin.entity.vo.account_application_vo import (
    AccountApplicationSubmitVO,
    AccountApplicationReviewVO
)
from module_admin.service.internal_user_login_service import InternalUserLoginService
from config.get_db import get_db
from utils.response_util import ResponseUtil
from exceptions.exception import ValidationException, BusinessException
from utils.log_util import logger

# 创建路由器
account_application_controller = APIRouter(prefix="/api/v1/account/application", tags=["开户申请"])


@account_application_controller.post('/submit', summary="提交开户申请")
async def submit_application(
    application_data: AccountApplicationSubmitVO,
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """提交开户申请接口
    
    提交个人开户申请信息
    
    Args:
        application_data: 申请数据
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        申请结果
    """
    try:
        # 获取当前用户手机号
        mobile = getattr(current_user.user, 'mobile', None)
        
        if not mobile:
            logger.warning("当前用户手机号获取失败")
            raise ValidationException(message="当前用户手机号获取失败")
            
        logger.info(f"用户 {mobile} 提交开户申请")
        
        # 调用服务层提交申请
        result = await AccountApplicationService.submit_application(
            query_db, application_data, mobile
        )
        
        return ResponseUtil.success(
            msg="开户申请提交成功",
            data=result
        )
        
    except ValidationException as e:
        logger.warning(f"提交开户申请参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"提交开户申请失败: {str(e)}")
        return ResponseUtil.error(msg="提交开户申请失败")


@account_application_controller.get('/status', summary="查询开户申请状态")
async def get_application_status(
    mobile: Optional[str] = Query(None, description="手机号（管理员可查询其他用户）"),
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """查询开户申请状态接口
    
    查询用户的开户申请状态
    
    Args:
        mobile: 手机号（可选，管理员可查询其他用户）
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        申请状态信息
    """
    try:
        # 如果没有指定手机号，使用当前用户的手机号
        if not mobile:
            mobile = getattr(current_user.user, 'mobile', None)
            
        if not mobile:
            logger.warning("手机号参数缺失")
            raise ValidationException(message="手机号参数缺失")
            
        logger.info(f"查询用户 {mobile} 的开户申请状态")
        
        # 调用服务层查询状态
        result = await AccountApplicationService.get_application_status(query_db, mobile)
        
        return ResponseUtil.success(
            msg="查询申请状态成功",
            data=result
        )
        
    except ValidationException as e:
        logger.warning(f"查询申请状态参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"查询申请状态失败: {str(e)}")
        return ResponseUtil.error(msg="查询申请状态失败")


@account_application_controller.get('/detail', summary="获取开户申请详情")
async def get_application_detail(
    uuid: str = Query(..., description="申请UUID"),
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取开户申请详情接口
    
    获取指定申请的详细信息
    
    Args:
        uuid: 申请UUID
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        申请详情信息
    """
    try:
        logger.info(f"获取开户申请详情，UUID: {uuid}")
        
        # 调用服务层获取详情
        result = await AccountApplicationService.get_application_detail(query_db, uuid)
        
        if not result:
            return ResponseUtil.error(msg="申请记录不存在")
        
        return ResponseUtil.success(
            msg="获取申请详情成功",
            data=result
        )
        
    except Exception as e:
        logger.error(f"获取申请详情失败: {str(e)}")
        return ResponseUtil.error(msg="获取申请详情失败")


@account_application_controller.get('/list', summary="获取开户申请列表")
async def get_application_list(
    page: int = Query(1, description="页码"),
    page_size: int = Query(20, description="每页数量"),
    status: Optional[str] = Query(None, description="申请状态"),
    mobile: Optional[str] = Query(None, description="手机号"),
    name: Optional[str] = Query(None, description="姓名"),
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取开户申请列表接口
    
    获取开户申请列表（管理员功能）
    
    Args:
        page: 页码
        page_size: 每页数量
        status: 申请状态
        mobile: 手机号
        name: 姓名
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        申请列表
    """
    try:
        logger.info(f"获取开户申请列表，页码: {page}, 每页: {page_size}")
        
        # 调用服务层获取列表
        result = await AccountApplicationService.get_application_list(
            query_db, page, page_size, status, mobile, name
        )
        
        return ResponseUtil.success(
            msg="获取申请列表成功",
            data=result
        )
        
    except Exception as e:
        logger.error(f"获取申请列表失败: {str(e)}")
        return ResponseUtil.error(msg="获取申请列表失败")


@account_application_controller.post('/review', summary="审核开户申请")
async def review_application(
    review_data: AccountApplicationReviewVO,
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """审核开户申请接口
    
    审核用户的开户申请（管理员功能）
    
    Args:
        review_data: 审核数据
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        审核结果
    """
    try:
        # 获取当前用户ID
        reviewer_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        
        if not reviewer_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")
            
        logger.info(f"审核人 {reviewer_id} 审核申请 {review_data.uuid}")
        
        # 调用服务层审核申请
        result = await AccountApplicationService.review_application(
            query_db, review_data, reviewer_id
        )
        
        return ResponseUtil.success(
            msg="审核成功",
            data=result
        )
        
    except ValidationException as e:
        logger.warning(f"审核申请参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"审核申请失败: {str(e)}")
        return ResponseUtil.error(msg="审核申请失败")


@account_application_controller.post('/yeepay/notify', summary="易宝开户异步通知")
async def yeepay_innet_notify(
    request: Request,
    query_db: AsyncSession = Depends(get_db)
):
    """易宝开户异步通知接口

    接收易宝开户的异步通知，处理开户结果

    Args:
        request: 请求对象
        query_db: 数据库会话

    Returns:
        处理结果
    """
    try:
        # 获取原始请求体
        body = await request.body()
        logger.info(f"=== 收到易宝开户异步通知 ===")
        logger.info(f"请求方法: {request.method}")
        logger.info(f"请求头: {dict(request.headers)}")
        logger.info(f"原始请求体: {body.decode('utf-8') if body else 'Empty'}")

        # 解析JSON数据
        try:
            if body:
                notify_data = json.loads(body.decode('utf-8'))
            else:
                logger.error("请求体为空")
                return ResponseUtil.error(msg="请求体为空", code=400)
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return ResponseUtil.error(msg="请求数据格式错误", code=400)

        logger.info(f"解析后的通知数据: {notify_data}")

        # 调用开户服务处理通知
        result = await AccountApplicationService.handle_yeepay_innet_notify_service(query_db, notify_data)

        # 易宝支付要求返回特定格式的响应
        response_data = {
            "code": "SUCCESS",
            "message": "处理成功",
            "result": result.get("status", "success")
        }

        logger.info(f"返回给易宝的响应: {response_data}")
        return response_data

    except Exception as e:
        logger.error(f"处理易宝开户通知异常: {str(e)}")
        import traceback
        logger.error(f"异常堆栈: {traceback.format_exc()}")

        # 即使处理失败，也要返回成功响应给易宝，避免重复通知
        return {
            "code": "SUCCESS",
            "message": "已收到通知"
        }


@account_application_controller.get('/yeepay/query/{application_uuid}', summary="查询易宝入网结果")
async def query_yeepay_innet_result(
    application_uuid: str,
    query_db: AsyncSession = Depends(get_db)
):
    """查询易宝入网结果接口

    根据申请UUID查询易宝入网结果

    Args:
        application_uuid: 申请UUID
        query_db: 数据库会话

    Returns:
        查询结果
    """
    try:
        logger.info(f"=== 查询易宝入网结果请求 ===")
        logger.info(f"申请UUID: {application_uuid}")

        # 调用查询服务
        result = await AccountApplicationService.query_yeepay_innet_result_service(
            query_db,
            application_uuid
        )

        logger.info(f"查询易宝入网结果成功")

        return ResponseUtil.success(
            data=result,
            msg="查询成功"
        )

    except BusinessException as e:
        logger.error(f"查询易宝入网结果业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message)
    except ValidationException as e:
        logger.warning(f"查询易宝入网结果参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"查询易宝入网结果失败: {str(e)}")
        return ResponseUtil.error(msg="查询失败")


@account_application_controller.get('/status/check/{mobile}', summary="检查开户状态")
async def check_account_status_by_mobile(
    mobile: str,
    query_db: AsyncSession = Depends(get_db)
):
    """根据手机号检查开户状态接口

    用于余额页面检查用户开户状态，自动查询易宝结果并更新状态

    Args:
        mobile: 手机号
        query_db: 数据库会话

    Returns:
        开户状态信息
    """
    try:
        logger.info(f"=== 检查开户状态请求 ===")
        logger.info(f"手机号: {mobile}")

        # 调用检查服务
        result = await AccountApplicationService.check_account_status_by_mobile_service(
            query_db,
            mobile
        )

        logger.info(f"检查开户状态完成: {result}")

        return ResponseUtil.success(
            data=result,
            msg="查询成功"
        )

    except BusinessException as e:
        logger.error(f"检查开户状态业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message)
    except ValidationException as e:
        logger.warning(f"检查开户状态参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"检查开户状态失败: {str(e)}")
        return ResponseUtil.error(msg="查询失败")


