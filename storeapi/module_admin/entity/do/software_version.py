"""
软件版本数据模型
用于软件版本信息的数据库映射
"""
from datetime import datetime
from sqlalchemy import Column, String, Integer, DECIMAL, SmallInteger, BigInteger, DateTime
from config.database import Base


class SoftwareVersion(Base):
    """软件版本表"""

    __tablename__ = 'software_version'

    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='ID')
    
    # 基本信息
    uuid = Column(String(36), nullable=False, unique=True, comment='UUID')
    name = Column(String(50), nullable=False, unique=True, comment='版本名称')
    description = Column(String(255), nullable=False, comment='版本描述')
    price = Column(DECIMAL(10, 2), nullable=False, comment='价格')
    
    # 状态信息
    is_available = Column(SmallInteger, nullable=False, default=1, comment='是否可用')
    sort_order = Column(Integer, nullable=True, default=0, comment='排序')
    
    # 系统字段
    create_time = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(String(64), nullable=True, comment='创建者')
    update_by = Column(String(64), nullable=True, comment='更新者')
    
    def __repr__(self):
        return f"<SoftwareVersion(id={self.id}, name='{self.name}', uuid='{self.uuid}')>"
