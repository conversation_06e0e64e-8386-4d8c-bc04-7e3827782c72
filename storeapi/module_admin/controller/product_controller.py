"""
产品管理控制器（员工入驻专用）
包含：
- 员工入驻时的产品选择接口
"""
from fastapi import APIRouter, Depends, Query, Body, Request, File, Form, UploadFile
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, Optional, List, Optional
from pydantic import BaseModel, Field

from config.get_db import get_db
from module_admin.service.product_service import ProductService
from module_admin.service.internal_user_login_service import InternalUserLoginService
from module_admin.entity.vo.staff_registration_vo import (
    StaffRegistrationRequestModel, StaffRegistrationResponseModel
)
from module_admin.entity.vo.staff_list_vo import (
    StaffListRequestModel, StaffListResponseModel
)
from module_admin.entity.vo.product_vo import (
    CreateProductRequestModel, CreateProductResponseModel
)
from utils.file_upload_util import FileUploadUtil
from sqlalchemy import text
from utils.log_util import logger
from utils.response_util import ResponseUtil
from exceptions.exception import (
    BusinessException, ValidationException, DatabaseException,
    ResourceNotFoundException, AuthException, QueryException
)
from utils.exception_util import ExceptionUtil


# 请求模型定义
class BatchUpdateStatusRequest(BaseModel):
    """批量更新产品状态请求模型"""
    product_ids: List[int] = Field(..., description="产品ID列表", min_items=1)
    status: int = Field(..., description="目标状态：1-上架，0-下架", ge=0, le=1)


# 创建产品管理API路由
product_controller = APIRouter(
    prefix="/api/v1/product",
    dependencies=[Depends(InternalUserLoginService.get_current_user)]
)

@product_controller.get(
    '/products',
    summary="获取产品列表（员工入驻专用）"
)
async def get_products(
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """获取产品列表（员工入驻专用）

    获取当前用户公司的产品列表，用于员工入驻时选择产品

    根据当前登录用户的公司ID，自动查询该公司的所有启用产品

    返回的产品包含基本信息：
    - id: 产品ID
    - name: 产品名称
    - category: 产品分类
    """
    try:
        # 获取当前用户的公司ID
        company_id = current_user.user.company_id
        if not company_id:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"获取公司 {company_id} 的产品列表")

        # 获取产品列表
        products = await ProductService.get_products_by_company(db, company_id)

        return ResponseUtil.success(
            data=products,
            msg=f"获取产品列表成功，共{len(products)}项产品"
        )
    except ValidationException as e:
        logger.error(f"参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except DatabaseException as e:
        logger.error(f"数据库操作失败: {e.message}")
        return ResponseUtil.error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"业务处理失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取产品列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@product_controller.post(
    '/staff/register',
    summary="员工入驻",
    response_model=Dict[str, Any]
)
async def register_staff(
    staff_data: StaffRegistrationRequestModel,
    invitation_code: Optional[str] = Query(None, description="邀请码"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """员工入驻接口

    创建新的服务员工，包括：
    - 员工基本信息（service_staff表）
    - 员工扩展信息（service_staff_ext表）
    - 员工产品关联（service_product表）

    Args:
        staff_data: 员工入驻数据
        db: 数据库会话
        current_user: 当前用户信息

    Returns:
        员工入驻结果
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        company_id = current_user.user.company_id
        store_id = current_user.user.store_id

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")

        if not company_id:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        if not store_id:
            logger.warning("当前用户没有关联的门店信息")
            raise ValidationException(message="用户没有关联的门店信息")

        logger.info(f"用户 {current_user_id} 开始员工入驻，姓名: {staff_data.real_name}, 手机: {staff_data.mobile}")

        # 调用服务层进行员工入驻
        # 门店端直接注册，如果有邀请码则为门店管理员邀请
        inviter_type = 'internal_user' if invitation_code else None
        result = await ProductService.register_staff(
            db=db,
            staff_data=staff_data,
            current_user_id=current_user_id,
            company_id=company_id,
            store_id=store_id,
            invitation_code=invitation_code,
            inviter_type=inviter_type
        )

        return ResponseUtil.success(
            data=result.model_dump(),
            msg=f"员工入驻成功，员工ID: {result.staff_id}"
        )
    except ValidationException as e:
        logger.error(f"员工入驻参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except DatabaseException as e:
        logger.error(f"员工入驻数据库操作失败: {e.message}")
        return ResponseUtil.error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"员工入驻业务处理失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"员工入驻异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


# 邀请码信息接口已移动到 public_controller.py 中，路径为 /api/v1/public/product/invitation-info/{invitation_code}


# 通过邀请码注册员工接口已移动到 public_controller.py 中，路径为 /api/v1/public/product/staff/register-by-invitation


@product_controller.post(
    '/staff/list',
    summary="查询员工列表",
    response_model=Dict[str, Any]
)
async def get_staff_list(
    request_data: StaffListRequestModel,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """查询员工列表接口

    根据当前用户的门店查询员工列表，支持分页、状态筛选和关键词搜索

    Args:
        request_data: 查询请求参数
        db: 数据库会话
        current_user: 当前用户信息

    Returns:
        员工列表和统计数据
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        store_uuid = current_user.user.store_uuid

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")

        if not store_uuid:
            logger.warning("当前用户没有关联的门店信息")
            raise ValidationException(message="用户没有关联的门店信息")

        logger.info(f"用户 {current_user_id} 查询员工列表，门店: {store_uuid}")

        # 调用服务层查询员工列表
        result = await ProductService.get_staff_list(
            db=db,
            request_data=request_data,
            current_user_id=current_user_id,
            store_uuid=store_uuid
        )

        return ResponseUtil.success(
            data=result.model_dump(),
            msg=f"查询员工列表成功，共{result.total}条记录"
        )
    except ValidationException as e:
        logger.error(f"员工列表查询参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except DatabaseException as e:
        logger.error(f"员工列表查询数据库操作失败: {e.message}")
        return ResponseUtil.error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"员工列表查询业务处理失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"员工列表查询异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@product_controller.get(
    '/purchasable-products',
    summary="获取可购买产品列表"
)
async def get_purchasable_products(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    search_keyword: Optional[str] = Query(None, description="搜索关键词"),
    service_skill_id: Optional[str] = Query(None, description="服务技能ID筛选"),
    db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """获取可购买产品列表接口

    根据当前用户公司的版本关联信息，获取可购买的产品列表

    业务逻辑：
    1. 根据公司ID查询company_version_relation获取已购买的版本
    2. 根据版本UUID查询version_skill_relation获取关联的技能
    3. 根据技能查询当前公司的产品
    4. 查询模板公司的所有产品
    5. 根据product_name去重，优先显示当前公司的产品

    Args:
        page: 页码，默认1
        size: 每页数量，默认20，最大100
        search_keyword: 搜索关键词，可选
        service_skill_id: 服务技能ID筛选，可选
        db: 数据库会话
        current_user: 当前登录用户信息

    Returns:
        产品列表响应数据
    """
    try:
        # 获取当前用户的公司ID
        company_id = current_user.user.company_id
        if not company_id:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"获取公司 {company_id} 的可购买产品列表")

        # 调用服务层获取产品列表
        result = await ProductService.get_purchasable_products_service(
            db, company_id, page, size, search_keyword, service_skill_id
        )

        return ResponseUtil.success(data=result, msg="获取可购买产品列表成功")

    except ValidationException as e:
        logger.error(f"获取可购买产品列表参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"获取可购买产品列表查询失败: {e.message}")
        return ResponseUtil.error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取可购买产品列表业务异常: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取可购买产品列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@product_controller.get(
    '/purchasable-product-categories',
    summary="获取可购买产品分类列表"
)
async def get_purchasable_product_categories(
    db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """获取可购买产品分类列表接口

    根据当前用户公司的版本关联信息，获取可购买产品的分类列表

    Returns:
        分类列表响应数据
    """
    try:
        # 获取当前用户的公司ID
        company_id = current_user.user.company_id
        if not company_id:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"获取公司 {company_id} 的可购买产品分类列表")

        # 调用服务层获取分类列表
        result = await ProductService.get_purchasable_product_categories_service(db, company_id)

        return ResponseUtil.success(data=result, msg="获取可购买产品分类列表成功")

    except ValidationException as e:
        logger.error(f"获取可购买产品分类列表参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"获取可购买产品分类列表查询失败: {e.message}")
        return ResponseUtil.error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取可购买产品分类列表业务异常: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取可购买产品分类列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@product_controller.get(
    '/purchasable-products-with-categories',
    summary="获取可购买产品列表和分类信息（优化版）"
)
async def get_purchasable_products_with_categories(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    keyword: Optional[str] = Query(None, description="搜索关键词"),
    db: AsyncSession = Depends(get_db),
    current_user=Depends(InternalUserLoginService.get_current_user)
):
    """获取可购买产品列表和分类信息接口（优化版）

    一次请求同时获取产品列表、分类信息和分页数据，减少网络往返，提升性能

    Args:
        page: 页码，从1开始
        size: 每页数量，最大100
        keyword: 搜索关键词，可选

    Returns:
        包含产品列表、分类列表和分页信息的响应数据
    """
    try:
        # 获取当前用户的公司ID
        company_id = current_user.user.company_id
        if not company_id:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"获取公司 {company_id} 的可购买产品和分类信息，页码: {page}, 每页: {size}")

        # 调用服务层获取产品和分类信息
        result = await ProductService.get_purchasable_products_with_categories_service(
            db, company_id, page, size, None, keyword
        )

        return ResponseUtil.success(data=result, msg="获取可购买产品和分类信息成功")

    except ValidationException as e:
        logger.error(f"获取可购买产品和分类信息参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"获取可购买产品和分类信息查询失败: {e.message}")
        return ResponseUtil.error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取可购买产品和分类信息业务异常: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取可购买产品和分类信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@product_controller.get(
    '/store-products',
    summary="获取门店产品列表"
)
async def get_store_products(
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """获取门店产品列表接口

    根据当前用户公司UUID获取该公司的所有产品列表，用于门店服务项目管理

    返回产品的基本信息和上下架状态，支持批量状态管理

    Args:
        db: 数据库会话
        current_user: 当前登录用户信息

    Returns:
        产品列表响应数据，包含产品ID、名称、状态等信息
    """
    try:
        # 获取当前用户的公司UUID和门店UUID
        company_uuid = current_user.user.company_id
        store_uuid = current_user.user.store_uuid

        if not company_uuid:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"获取公司 {company_uuid} 门店 {store_uuid} 的产品列表")

        # 调用服务层获取门店产品列表（支持门店级别状态控制）
        result = await ProductService.get_store_products_service(db, company_uuid, store_uuid)

        return ResponseUtil.success(data=result, msg="获取门店产品列表成功")

    except ValidationException as e:
        logger.error(f"获取门店产品列表参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"获取门店产品列表查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取门店产品列表业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取门店产品列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@product_controller.post(
    '/batch-update-status',
    summary="批量更新产品状态"
)
async def batch_update_product_status(
    request_data: BatchUpdateStatusRequest,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """批量更新产品状态接口

    支持批量更新多个产品的上下架状态

    Args:
        request_data: 请求数据，包含产品ID列表和目标状态
            {
                "product_ids": [1, 2, 3],  # 产品ID列表
                "status": 1  # 目标状态：1-上架，0-下架
            }
        db: 数据库会话
        current_user: 当前登录用户信息

    Returns:
        更新结果响应数据
    """
    logger.info(f"批量更新产品状态接口被调用，请求数据: {request_data}")
    logger.info(f"当前用户信息: {current_user.user if current_user else 'None'}")
    try:
        # 获取当前用户的公司UUID和门店UUID
        company_uuid = getattr(current_user.user, 'company_id', None)
        store_uuid = getattr(current_user.user, 'store_uuid', None)

        if not company_uuid:
            logger.warning(f"当前用户没有关联的公司信息，用户信息: {current_user.user}")
            raise ValidationException(message="用户没有关联的公司信息")

        # 获取请求参数（Pydantic模型已经验证过了）
        product_ids = request_data.product_ids
        status = request_data.status

        logger.info(f"批量更新产品状态，公司: {company_uuid}, 门店: {store_uuid}, 产品ID: {product_ids}, 目标状态: {status}")

        # 额外验证产品ID列表
        if not all(isinstance(pid, int) and pid > 0 for pid in product_ids):
            raise ValidationException(message="产品ID必须为正整数")

        # 调用服务层批量更新产品状态（支持门店级别黑名单控制）
        result = await ProductService.batch_update_products_status_service(
            db, company_uuid, product_ids, status, store_uuid
        )

        return ResponseUtil.success(data=result, msg="批量更新产品状态成功")

    except ValidationException as e:
        logger.error(f"批量更新产品状态参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"批量更新产品状态查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"批量更新产品状态业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"批量更新产品状态异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@product_controller.post(
    '/batch-update-status-debug',
    summary="批量更新产品状态调试接口"
)
async def batch_update_product_status_debug(
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """调试版本的批量更新接口，用于诊断422错误"""
    try:
        # 获取原始请求体
        body = await request.body()
        logger.info(f"原始请求体: {body}")

        # 尝试解析JSON
        import json
        try:
            json_data = json.loads(body)
            logger.info(f"解析后的JSON数据: {json_data}")
        except Exception as e:
            logger.error(f"JSON解析失败: {e}")
            return ResponseUtil.failure(msg=f"JSON解析失败: {e}")

        # 获取Content-Type
        content_type = request.headers.get('content-type')
        logger.info(f"Content-Type: {content_type}")

        # 尝试手动创建模型
        try:
            request_model = BatchUpdateStatusRequest(**json_data)
            logger.info(f"模型创建成功: {request_model}")
        except Exception as e:
            logger.error(f"模型创建失败: {e}")
            return ResponseUtil.failure(msg=f"模型创建失败: {e}")

        return ResponseUtil.success(data={
            "message": "调试成功",
            "request_data": request_model.model_dump(),
            "user_company": getattr(current_user.user, 'company_id', None)
        })

    except Exception as e:
        logger.error(f"调试接口异常: {e}")
        return ResponseUtil.error(msg=f"调试接口异常: {e}")


@product_controller.get(
    '/detail/{product_id}',
    summary="获取产品详情"
)
async def get_product_detail(
    product_id: int,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """获取产品详情接口

    根据产品ID获取产品的详细信息，包括产品昵称、主图、详情图、视频等

    Args:
        product_id: 产品ID
        db: 数据库会话
        current_user: 当前登录用户信息

    Returns:
        产品详情响应数据
    """
    try:
        # 获取当前用户的公司UUID
        company_uuid = getattr(current_user.user, 'company_id', None)
        if not company_uuid:
            logger.warning(f"当前用户没有关联的公司信息，用户信息: {current_user.user}")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"获取产品详情，产品ID: {product_id}, 公司: {company_uuid}")

        # 调用服务层获取产品详情
        result = await ProductService.get_product_detail_service(db, product_id, company_uuid)

        return ResponseUtil.success(data=result, msg="获取产品详情成功")

    except ValidationException as e:
        logger.error(f"获取产品详情参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"产品不存在: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"获取产品详情查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取产品详情业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取产品详情异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@product_controller.post(
    '/create',
    summary="创建新产品",
    response_model=Dict[str, Any]
)
async def create_product(
    product_data: CreateProductRequestModel,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """创建新产品接口

    创建新的服务产品，包括：
    - 产品基本信息
    - 产品图片关联
    - 服务技能关联

    Args:
        product_data: 产品创建数据
        db: 数据库会话
        current_user: 当前登录用户信息

    Returns:
        创建结果响应数据
    """
    try:
        # 获取当前用户的公司UUID
        company_uuid = getattr(current_user.user, 'company_id', None)
        if not company_uuid:
            logger.warning(f"当前用户没有关联的公司信息，用户信息: {current_user.user}")
            raise ValidationException(message="用户没有关联的公司信息")

        # 获取当前用户ID
        current_user_id = str(current_user.user.id)

        logger.info(f"创建产品，产品名称: {product_data.product_name}, 公司: {company_uuid}, 用户: {current_user_id}")

        # 数据验证：确保必要的图片信息存在
        if not product_data.main_image_id:
            logger.warning(f"产品创建缺少主图ID，产品名称: {product_data.product_name}")
            # 可以选择抛出异常或使用默认值
            # raise ValidationException(message="产品主图不能为空")

        # 调用服务层创建产品
        result = await ProductService.create_product_service(
            db=db,
            company_uuid=company_uuid,
            store_uuid=None,  # 暂时不需要门店UUID
            current_user_id=current_user_id,
            product_data=product_data
        )

        return ResponseUtil.success(data=result, msg="产品创建成功")

    except ValidationException as e:
        logger.error(f"创建产品参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except DatabaseException as e:
        logger.error(f"创建产品数据库操作失败: {e.message}")
        return ResponseUtil.error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"创建产品业务处理失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"创建产品异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@product_controller.post(
    '/upload-image',
    summary="产品图片上传",
    response_model=Dict[str, Any]
)
async def upload_product_image(
    file: UploadFile = File(..., description="图片文件"),
    image_type: str = Form(..., description="图片类型：main（主图）或detail（详情图）"),
    product_name: str = Form(..., description="产品名称"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """产品图片上传接口

    专门用于产品创建时的图片上传，会按照指定格式创建数据库记录：
    - file_main表的remark格式：产品主图-{产品名称} 或 产品详情图-{产品名称}
    - file_item表存储对应的图片信息

    Args:
        file: 图片文件
        image_type: 图片类型（main/detail）
        product_name: 产品名称
        db: 数据库会话
        current_user: 当前登录用户信息

    Returns:
        上传结果，包含数据库记录ID和图片URL
    """
    try:
        # 获取当前用户信息
        current_user_id = str(current_user.user.id)
        user_name = current_user.user.name or 'unknown'

        # 根据图片类型生成remark
        if image_type == 'main':
            remark = f"产品主图-{product_name}"
        elif image_type == 'detail':
            remark = f"产品详情图-{product_name}"
        else:
            raise ValidationException(message="图片类型只能是main或detail")

        logger.info(f"产品图片上传，类型: {image_type}, 产品名称: {product_name}, 用户: {user_name}")

        # 创建文件上传工具类实例
        file_upload_util = FileUploadUtil()

        # 上传文件并创建数据库记录
        file_info = await file_upload_util.upload_file(
            file=file,
            file_name=file.filename,
            route="product",
            created_by=user_name,
            db=db
        )

        # 更新file_main表的remark字段为指定格式
        update_remark_sql = """
            UPDATE file_main
            SET remark = :remark
            WHERE id = :main_id
        """

        await db.execute(text(update_remark_sql), {
            "remark": remark,
            "main_id": file_info["id"]
        })
        await db.commit()

        logger.info(f"产品图片上传成功，文件ID: {file_info['id']}, URL: {file_info['file_url']}")

        return ResponseUtil.success(
            data={
                "id": file_info["id"],
                "file_url": file_info["file_url"],
                "file_name": file_info["file_name"],
                "file_size": file_info["file_size"],
                "file_type": file_info["file_type"],
                "remark": remark
            },
            msg="图片上传成功"
        )

    except ValidationException as e:
        logger.error(f"产品图片上传参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"产品图片上传异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@product_controller.put(
    '/update/{product_id}',
    summary="更新产品信息",
    response_model=Dict[str, Any]
)
async def update_product(
    product_id: int,
    product_data: CreateProductRequestModel,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """更新产品信息接口

    更新指定产品的信息，包括：
    - 产品基本信息
    - 产品图片关联
    - 服务技能关联
    - SKU信息

    Args:
        product_id: 产品ID
        product_data: 产品更新数据
        db: 数据库会话
        current_user: 当前登录用户信息

    Returns:
        更新结果响应数据
    """
    try:
        # 获取当前用户的公司UUID
        company_uuid = getattr(current_user.user, 'company_id', None)
        if not company_uuid:
            logger.warning(f"当前用户没有关联的公司信息，用户信息: {current_user.user}")
            raise ValidationException(message="用户没有关联的公司信息")

        # 获取当前用户ID
        current_user_id = str(current_user.user.id)

        logger.info(f"更新产品，产品ID: {product_id}, 产品名称: {product_data.product_name}, 公司: {company_uuid}, 用户: {current_user_id}")

        # 调用服务层更新产品
        result = await ProductService.update_product_service(
            db=db,
            product_id=product_id,
            company_uuid=company_uuid,
            current_user_id=current_user_id,
            product_data=product_data
        )

        return ResponseUtil.success(
            data=result,
            msg="产品更新成功"
        )

    except (ValidationException, BusinessException, DatabaseException, ResourceNotFoundException, AuthException) as e:
        logger.error(f"更新产品业务异常: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"更新产品异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
