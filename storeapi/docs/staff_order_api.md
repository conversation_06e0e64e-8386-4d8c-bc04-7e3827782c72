# 员工端订单接口文档

## 概述

员工端订单接口为员工提供查看和管理自己相关订单的功能。员工可以查看分配给自己的订单列表，包括待确认、进行中、已完成等各种状态的订单。

## 认证方式

所有接口都需要员工登录认证，使用 `ServiceStaffLoginService.get_current_staff` 进行身份验证。

## 接口列表

### 1. 获取员工订单列表 (POST)

**接口地址：** `POST /api/v1/staff-order/getOrderList`

**请求参数：**
```json
{
  "page": 1,           // 页码，默认1
  "size": 20,          // 每页数量，默认20，最大100
  "status": "20",      // 可选，订单状态筛选
  "keyword": "张三"    // 可选，关键词搜索（订单号、客户姓名、手机号）
}
```

**订单状态说明：**
- 10: 已接单
- 20: 派单待确认
- 30: 拒绝接单
- 40: 已派单
- 50: 执行中
- 60: 开始服务
- 70: 服务结束
- 80: 已完成
- 90: 已评价
- 99: 已取消

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取订单列表成功",
  "data": {
    "list": [
      {
        "order_number": "2025032208001",
        "order_status": 20,
        "order_status_name": "派单待确认",
        "product_name": "洗鞋修鞋",
        "product_type_name": "单次",
        "customer_name": "张三",
        "customer_mobile": "181****5920",
        "service_address": "福建省厦门市思明区湖滨东路***号厦门人才市场D体楼",
        "service_date": "2025-03-22",
        "service_time": "08:00",
        "pay_actual": 8.00,
        "service_personal": 0.29,
        "create_time": "2025-03-21T09:57:33",
        "update_time": "2025-03-21T09:57:33",
        "distance": "0.29km"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 20,
    "stat": {
      "pending_confirm": 1,    // 待确认订单数
      "in_service": 0,         // 服务中订单数
      "completed": 0,          // 已完成订单数
      "total": 1,              // 总订单数
      "total_commission": 0.29 // 总佣金
    }
  },
  "success": true
}
```

### 2. 获取员工订单列表 (GET)

**接口地址：** `GET /api/v1/staff-order/getOrderList`

**请求参数：** 通过URL参数传递
```
GET /api/v1/staff-order/getOrderList?page=1&size=20&status=20&keyword=张三
```

**响应格式：** 与POST接口相同

### 3. 获取员工订单统计

**接口地址：** `GET /api/v1/staff-order/getOrderStatistics`

**请求参数：** 无

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取订单统计成功",
  "data": {
    "pending_confirm": 1,    // 待确认订单数
    "in_service": 0,         // 服务中订单数
    "completed": 0,          // 已完成订单数
    "total": 1,              // 总订单数
    "total_commission": 0.29 // 总佣金
  },
  "success": true
}
```

## 前端调用示例

### JavaScript调用示例

```javascript
import { getStaffOrderList, getStaffOrderStatistics } from '@/api/staff-order.js';

// 获取订单列表
async function loadOrderList() {
  try {
    const response = await getStaffOrderList({
      page: 1,
      size: 20,
      status: '20' // 只查看待确认的订单
    });
    
    if (response && response.data) {
      console.log('订单列表:', response.data.list);
      console.log('统计信息:', response.data.stat);
    }
  } catch (error) {
    console.error('获取订单列表失败:', error);
  }
}

// 获取统计信息
async function loadStatistics() {
  try {
    const response = await getStaffOrderStatistics();
    
    if (response && response.data) {
      console.log('统计信息:', response.data);
    }
  } catch (error) {
    console.error('获取统计信息失败:', error);
  }
}
```

### Vue.js页面示例

```vue
<template>
  <view class="order-list">
    <!-- 统计卡片 -->
    <view class="stats-card">
      <view class="stat-item">
        <text class="stat-number">{{ statistics.pending_confirm }}</text>
        <text class="stat-label">待确认</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ statistics.in_service }}</text>
        <text class="stat-label">服务中</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ statistics.completed }}</text>
        <text class="stat-label">已完成</text>
      </view>
    </view>
    
    <!-- 订单列表 -->
    <view class="order-item" v-for="order in orderList" :key="order.order_number">
      <view class="order-header">
        <text class="order-number">{{ order.order_number }}</text>
        <text class="order-status">{{ order.order_status_name }}</text>
      </view>
      <view class="order-content">
        <text class="product-name">{{ order.product_name }}</text>
        <text class="customer-info">{{ order.customer_name }} {{ order.customer_mobile }}</text>
        <text class="service-address">{{ order.service_address }}</text>
        <text class="service-time">{{ order.service_date }} {{ order.service_time }}</text>
        <text class="commission">佣金: ¥{{ order.service_personal }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { getStaffOrderList, getStaffOrderStatistics } from '@/api/staff-order.js';

export default {
  data() {
    return {
      orderList: [],
      statistics: {},
      currentPage: 1,
      pageSize: 20
    };
  },
  
  onLoad() {
    this.loadData();
  },
  
  methods: {
    async loadData() {
      await Promise.all([
        this.loadOrderList(),
        this.loadStatistics()
      ]);
    },
    
    async loadOrderList() {
      try {
        const response = await getStaffOrderList({
          page: this.currentPage,
          size: this.pageSize
        });
        
        if (response && response.data) {
          this.orderList = response.data.list;
        }
      } catch (error) {
        console.error('获取订单列表失败:', error);
        uni.showToast({
          title: '获取订单列表失败',
          icon: 'none'
        });
      }
    },
    
    async loadStatistics() {
      try {
        const response = await getStaffOrderStatistics();
        
        if (response && response.data) {
          this.statistics = response.data;
        }
      } catch (error) {
        console.error('获取统计信息失败:', error);
      }
    }
  }
};
</script>
```

## 错误处理

接口遵循统一的错误处理规范：

- **400**: 参数验证失败
- **401**: 未登录或token过期
- **403**: 权限不足
- **500**: 服务器内部错误

错误响应格式：
```json
{
  "code": 400,
  "msg": "页码必须大于0",
  "data": null,
  "success": false
}
```
