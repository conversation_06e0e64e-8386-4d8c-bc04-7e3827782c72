from fastapi import Request, HTTPException
from fastapi.responses import HTMLResponse
from config.env import AppConfig


async def swagger_auth_middleware(request: Request, call_next):
    """
    Swagger文档访问认证中间件
    """
    # 检查是否是访问 Swagger 文档的请求
    if request.url.path == "/docs":
        # 获取认证信息
        auth_header = request.headers.get('Authorization')
        if not auth_header:
            # 如果没有认证信息，返回登录页面
            return HTMLResponse(
                """
                <html>
                    <head>
                        <title>Swagger UI 认证</title>
                        <style>
                            body {
                                font-family: Arial, sans-serif;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                height: 100vh;
                                margin: 0;
                                background-color: #f5f5f5;
                            }
                            .login-container {
                                background: white;
                                padding: 20px;
                                border-radius: 8px;
                                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                            }
                            .form-group {
                                margin-bottom: 15px;
                            }
                            label {
                                display: block;
                                margin-bottom: 5px;
                            }
                            input {
                                width: 100%;
                                padding: 8px;
                                border: 1px solid #ddd;
                                border-radius: 4px;
                                box-sizing: border-box;
                            }
                            button {
                                width: 100%;
                                padding: 10px;
                                background-color: #4CAF50;
                                color: white;
                                border: none;
                                border-radius: 4px;
                                cursor: pointer;
                            }
                            button:hover {
                                background-color: #45a049;
                            }
                        </style>
                    </head>
                    <body>
                        <div class="login-container">
                            <h2>Swagger UI 认证</h2>
                            <form id="loginForm">
                                <div class="form-group">
                                    <label for="username">用户名:</label>
                                    <input type="text" id="username" name="username" required>
                                </div>
                                <div class="form-group">
                                    <label for="password">密码:</label>
                                    <input type="password" id="password" name="password" required>
                                </div>
                                <button type="submit">登录</button>
                            </form>
                        </div>
                        <script>
                            document.getElementById('loginForm').addEventListener('submit', function(e) {
                                e.preventDefault();
                                const username = document.getElementById('username').value;
                                const password = document.getElementById('password').value;
                                
                                // 创建 Basic Auth 认证头
                                const auth = btoa(username + ':' + password);
                                
                                // 重定向到 Swagger UI，带上认证头
                                window.location.href = '/docs?auth=' + auth;
                            });
                        </script>
                    </body>
                </html>
                """
            )
        
        # 验证认证信息
        try:
            auth_type, auth_string = auth_header.split(' ', 1)
            if auth_type.lower() != 'basic':
                raise HTTPException(status_code=401, detail="Invalid authentication method")
            
            import base64
            decoded = base64.b64decode(auth_string).decode('utf-8')
            username, password = decoded.split(':', 1)
            
            # 验证用户名和密码
            if username != AppConfig.swagger_username or password != AppConfig.swagger_password:
                raise HTTPException(status_code=401, detail="Invalid credentials")
            
            # 认证通过，继续处理请求
            response = await call_next(request)
            return response
            
        except Exception as e:
            raise HTTPException(status_code=401, detail="Authentication failed")
    
    # 如果不是访问 Swagger 文档的请求，直接继续处理
    response = await call_next(request)
    return response 