"""
自动审核服务
复制yunapi的审核逻辑，实现商户入驻申请的自动审核
"""
import logging
from typing import Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.experience_vo import ExperienceRegisterRequest
from utils.log_util import logger
from utils.common_util import CommonUtil
from utils.pwd_util import PwdUtil
from exceptions.exception import BusinessException, ValidationException

logger = logging.getLogger(__name__)


class AutoAuditService:
    """自动审核服务类"""

    @classmethod
    async def auto_audit_merchant_service(
        cls,
        query_db: AsyncSession,
        experience_id: int,
        register_data: ExperienceRegisterRequest
    ) -> CrudResponseModel:
        """
        自动审核商户并创建公司、门店、门店信息和软件版本关联
        
        Args:
            query_db: 数据库会话
            experience_id: 商户ID
            register_data: 注册数据
            
        Returns:
            审核结果
        """
        try:
            logger.info(f"开始自动审核商户，ID: {experience_id}")
            
            # 1. 创建公司、门店和版本关联
            create_result = await cls._create_company_with_store(
                query_db, register_data
            )
            
            # 获取创建的公司和门店信息
            company_dict = create_result.result.get('company')
            store_dict = create_result.result.get('store')
            
            # 2. 为新创建的公司添加支付类型
            if company_dict:
                pay_types_result = await cls._create_pay_types(
                    query_db, company_dict['id']
                )
                logger.info(f'为公司{company_dict["id"]}创建支付类型成功：{pay_types_result.message}')
                
                # 3. 为新创建的公司添加初始产品
                products_result = await cls._create_initial_products(
                    query_db, company_dict['id']
                )
                logger.info(f'为公司{company_dict["id"]}创建初始产品成功：{products_result.message}')
                
                # 4. 创建内部用户账号
                user_result = await cls._create_internal_user(
                    query_db,
                    company_dict['id'],
                    store_dict['store_uuid'],
                    store_dict['id'],
                    company_dict['name'],
                    store_dict['name'],
                    register_data
                )
                logger.info(f'创建内部用户账号成功：{user_result.message}')
                
                # 5. 更新商户状态为通过(1)
                update_status_result = await cls._update_experience_status(
                    query_db, experience_id, 1
                )
                logger.info(f'更新商户状态成功：{update_status_result.message}')
                
                # 提交事务
                await query_db.commit()
                
                logger.info(f'自动审核商户成功：创建公司、门店和版本关联成功，创建支付类型成功，创建初始产品成功，创建内部用户账号成功，并更新id为{experience_id}的商户状态为通过(1)')
                
                return CrudResponseModel(
                    is_success=True,
                    message=f"自动审核通过：{create_result.message}，{pay_types_result.message}，{products_result.message}，{update_status_result.message}，{user_result.message}",
                    result={
                        'company': company_dict,
                        'store': store_dict,
                        'user': user_result.result
                    }
                )
            
            # 如果没有公司信息，只提交事务
            await query_db.commit()
            
            logger.info(f'自动审核商户成功：创建公司、门店和版本关联成功，并更新id为{experience_id}的商户状态为通过(1)')
            return CrudResponseModel(
                is_success=True,
                message=f"自动审核通过：{create_result.message}",
                result={'company': company_dict, 'store': store_dict}
            )
            
        except Exception as e:
            # 回滚事务
            await query_db.rollback()
            logger.error(f"自动审核商户失败: {str(e)}")
            raise BusinessException(message=f"自动审核失败: {str(e)}")

    @classmethod
    async def _create_company_with_store(
        cls,
        query_db: AsyncSession,
        register_data: ExperienceRegisterRequest
    ) -> CrudResponseModel:
        """
        创建公司并关联门店和软件版本
        """
        # 获取公司名称
        company_name = register_data.company_name
        
        # 1. 插入公司表数据
        company_uuid = CommonUtil.get_uuid_without_hyphen()
        company_data = {
            'id': company_uuid,
            'name': register_data.license_name or company_name,
            'city_id': None,
            'city': None,
            'address': register_data.detail_address,
            'address_desc': register_data.legal_address,
            'balance': 0.00,
            'status': '1',
            'is_delete': '0'
        }
        
        # 插入公司数据
        company_insert_query = text("""
            INSERT INTO company (id, name, city_id, city, address, address_desc, balance, status, is_delete)
            VALUES (:id, :name, :city_id, :city, :address, :address_desc, :balance, :status, :is_delete)
        """)
        await query_db.execute(company_insert_query, company_data)
        
        # 2. 插入门店表数据
        store_uuid = CommonUtil.get_compact_id(length=10)
        store_data = {
            'store_uuid': store_uuid,
            'company_id': company_uuid,
            'name': company_name,
            'phone': register_data.phone_number,
            'mobile': register_data.phone_number,
            'address': register_data.detail_address,
            'manager': register_data.legal_person_name,
            'status': 1,
            'store_status': 1,
            'remark': '由商户表自动创建',
            'introduce': register_data.detail_address or company_name,
            'email': '<EMAIL>',
            'is_new': 1,
            'level': 'A',
            'flag': 0,
            'is_show_wxapp': 1,
            'is_delete': 0,
            'created_by': 'admin',
            'updated_by': 'admin'
        }
        
        # 插入门店数据并获取ID
        store_insert_query = text("""
            INSERT INTO store (store_uuid, company_id, name, phone, mobile, address, manager, status, store_status, remark, introduce, email, is_new, level, flag, is_show_wxapp, is_delete, created_by, updated_by)
            VALUES (:store_uuid, :company_id, :name, :phone, :mobile, :address, :manager, :status, :store_status, :remark, :introduce, :email, :is_new, :level, :flag, :is_show_wxapp, :is_delete, :created_by, :updated_by)
        """)
        await query_db.execute(store_insert_query, store_data)
        
        # 获取刚插入的门店ID
        store_id_query = text("SELECT id FROM store WHERE store_uuid = :store_uuid")
        store_id_result = await query_db.execute(store_id_query, {'store_uuid': store_uuid})
        store_id = store_id_result.scalar()
        
        # 3. 插入store_info表数据
        store_info_data = {
            'store_id': store_id,
            'business_license': register_data.license_code,
            'is_invoice': 1,
            'invoice_title': register_data.license_name,
            'invoice_code': register_data.license_code,
            'invoice_phone': register_data.phone_number,
            'invoice_address': register_data.detail_address,
            'is_bd_pos': 0,
            'location_synchronize_status': 1,
            'health_degree': 95.5,
            'is_clean_keeping': 1,
            'is_housekeeping_staff': 1,
            'is_part_time_job': 1,
            'is_free': 0,
            'is_discount': 0,
            'user_num_limit': 100,
            'pay_num_limit': 100
        }
        
        store_info_insert_query = text("""
            INSERT INTO store_info (store_id, business_license, is_invoice, invoice_title, invoice_code, invoice_phone, invoice_address, is_bd_pos, location_synchronize_status, health_degree, is_clean_keeping, is_housekeeping_staff, is_part_time_job, is_free, is_discount, user_num_limit, pay_num_limit)
            VALUES (:store_id, :business_license, :is_invoice, :invoice_title, :invoice_code, :invoice_phone, :invoice_address, :is_bd_pos, :location_synchronize_status, :health_degree, :is_clean_keeping, :is_housekeeping_staff, :is_part_time_job, :is_free, :is_discount, :user_num_limit, :pay_num_limit)
        """)
        await query_db.execute(store_info_insert_query, store_info_data)

        # 4. 创建软件版本关联（默认使用ID为1的版本）
        await cls._create_company_version_relation(query_db, company_uuid)
        
        # 将ORM对象转换为字典，避免延迟加载问题
        company_dict = {
            'id': company_uuid,
            'name': company_name
        }
        
        store_dict = {
            'id': store_id,
            'store_uuid': store_uuid,
            'name': company_name
        }
        
        # 返回创建的公司和门店信息，以便后续使用
        return CrudResponseModel(
            is_success=True,
            message='创建公司和门店成功',
            result={'company': company_dict, 'store': store_dict}
        )

    @classmethod
    async def _create_pay_types(
        cls,
        query_db: AsyncSession,
        company_uuid: str
    ) -> CrudResponseModel:
        """
        为公司创建默认支付类型
        """
        try:
            # 定义五种默认支付类型
            pay_types_config = [
                {
                    'company_uuid': company_uuid,
                    'pay_type_code': 'BALANCE',
                    'pay_type_name': '余额支付',
                    'icon_url': None,
                    'is_enabled': 1,
                    'is_preset': 1,
                    'channel_config': '{"description":"使用账户余额支付"}',
                    'sort_order': 1,
                    'remark': None,
                    'created_by': 'system',
                    'updated_by': None
                },
                {
                    'company_uuid': company_uuid,
                    'pay_type_code': 'WECHAT',
                    'pay_type_name': '微信支付',
                    'icon_url': None,
                    'is_enabled': 1,
                    'is_preset': 1,
                    'channel_config': '{"app_id":"","mch_id":"","api_key":"","cert_path":""}',
                    'sort_order': 2,
                    'remark': None,
                    'created_by': 'system',
                    'updated_by': None
                },
                {
                    'company_uuid': company_uuid,
                    'pay_type_code': 'ALIPAY',
                    'pay_type_name': '支付宝支付',
                    'icon_url': None,
                    'is_enabled': 1,
                    'is_preset': 1,
                    'channel_config': '{"app_id":"","private_key":"","public_key":"","gateway":""}',
                    'sort_order': 3,
                    'remark': None,
                    'created_by': 'system',
                    'updated_by': None
                },
                {
                    'company_uuid': company_uuid,
                    'pay_type_code': 'UNIONPAY',
                    'pay_type_name': '银联支付',
                    'icon_url': None,
                    'is_enabled': 1,
                    'is_preset': 1,
                    'channel_config': '{"merchant_id":"","terminal_id":"","access_type":""}',
                    'sort_order': 4,
                    'remark': None,
                    'created_by': 'system',
                    'updated_by': None
                },
                {
                    'company_uuid': company_uuid,
                    'pay_type_code': 'CASH',
                    'pay_type_name': '现金支付',
                    'icon_url': None,
                    'is_enabled': 1,
                    'is_preset': 1,
                    'channel_config': '{"description":"现金支付方式"}',
                    'sort_order': 5,
                    'remark': None,
                    'created_by': 'system',
                    'updated_by': None
                }
            ]

            # 批量创建支付类型
            for pay_type_data in pay_types_config:
                pay_type_insert_query = text("""
                    INSERT INTO pay_type (company_uuid, pay_type_code, pay_type_name, icon_url, is_enabled, is_preset, channel_config, sort_order, remark, created_by, updated_by)
                    VALUES (:company_uuid, :pay_type_code, :pay_type_name, :icon_url, :is_enabled, :is_preset, :channel_config, :sort_order, :remark, :created_by, :updated_by)
                """)
                await query_db.execute(pay_type_insert_query, pay_type_data)

            return CrudResponseModel(is_success=True, message=f'成功创建{len(pay_types_config)}种支付类型', result={'pay_types_count': len(pay_types_config)})

        except Exception as e:
            raise BusinessException(message=f'创建支付类型失败: {str(e)}')

    @classmethod
    async def _create_initial_products(
        cls,
        query_db: AsyncSession,
        company_uuid: str
    ) -> CrudResponseModel:
        """
        为公司创建初始产品（复制模板产品）
        """
        try:
            # 1. 获取模板产品数据（id 48-68）
            template_products_query = text("""
                SELECT * FROM product WHERE id BETWEEN 48 AND 68 AND is_delete = 0
            """)
            template_products_result = await query_db.execute(template_products_query)
            template_products = template_products_result.fetchall()

            if not template_products:
                raise BusinessException(message='未找到模板产品数据')

            # 2. 获取模板产品的ID列表
            template_product_ids = [product.id for product in template_products]

            # 3. 获取对应的SKU数据
            if template_product_ids:
                placeholders = ','.join([f':id_{i}' for i in range(len(template_product_ids))])
                sku_query = text(f"""
                    SELECT * FROM product_sku WHERE productid IN ({placeholders})
                """)
                sku_params = {f'id_{i}': product_id for i, product_id in enumerate(template_product_ids)}
                template_skus_result = await query_db.execute(sku_query, sku_params)
                template_skus = template_skus_result.fetchall()
            else:
                template_skus = []

            # 4. 复制产品数据
            new_products = []
            product_id_mapping = {}

            for template_product in template_products:
                new_product_data = {
                    'company_uuid': company_uuid,
                    'product_name': template_product.product_name,
                    'img_id': template_product.img_id,
                    'serve_type_name': template_product.serve_type_name,
                    'service_skill_id': template_product.service_skill_id,
                    'service_skill_name': template_product.service_skill_name,
                    'service_skill_main_id': template_product.service_skill_main_id,
                    'service_skill_main_name': template_product.service_skill_main_name,
                    'online_store_num': template_product.online_store_num,
                    'is_all_support_store': template_product.is_all_support_store,
                    'sum_num': template_product.sum_num,
                    'is_delete': 0,
                    'is_open_service_phone': template_product.is_open_service_phone,
                    'type': template_product.type,
                    'type_name': template_product.type_name,
                    'display_edit': template_product.display_edit,
                    'display_delete': template_product.display_delete,
                    'display_detail': template_product.display_detail,
                    'display_edit_product_detail': template_product.display_edit_product_detail,
                    'is_gaode_line': template_product.is_gaode_line,
                    'op_user_name': 'system',
                    'op_time': datetime.now(),
                    'product_status': template_product.product_status,
                    'details': template_product.details,
                    'min_number': template_product.min_number,
                    'max_number': template_product.max_number,
                    'video_id': template_product.video_id,
                    'uuid': CommonUtil.get_uuid_without_hyphen()
                }

                # 插入产品并获取新ID
                product_insert_query = text("""
                    INSERT INTO product (
                        company_uuid, product_name, img_id, serve_type_name,
                        service_skill_id, service_skill_name, service_skill_main_id, service_skill_main_name,
                        online_store_num, is_all_support_store, sum_num, is_delete,
                        is_open_service_phone, type, type_name, display_edit,
                        display_delete, display_detail, display_edit_product_detail, is_gaode_line,
                        op_user_name, op_time, product_status, details,
                        min_number, max_number, video_id, uuid
                    ) VALUES (
                        :company_uuid, :product_name, :img_id, :serve_type_name,
                        :service_skill_id, :service_skill_name, :service_skill_main_id, :service_skill_main_name,
                        :online_store_num, :is_all_support_store, :sum_num, :is_delete,
                        :is_open_service_phone, :type, :type_name, :display_edit,
                        :display_delete, :display_detail, :display_edit_product_detail, :is_gaode_line,
                        :op_user_name, :op_time, :product_status, :details,
                        :min_number, :max_number, :video_id, :uuid
                    )
                """)
                result = await query_db.execute(product_insert_query, new_product_data)

                # 获取新插入的产品ID
                new_product_id_query = text("SELECT LAST_INSERT_ID()")
                new_product_id_result = await query_db.execute(new_product_id_query)
                new_product_id = new_product_id_result.scalar()

                # 建立ID映射关系
                product_id_mapping[template_product.id] = new_product_id
                new_products.append(new_product_data)

            # 5. 复制SKU数据
            new_skus = []
            for sku in template_skus:
                new_product_id = product_id_mapping.get(sku.productid)
                if new_product_id:
                    sku_data = {
                        'productid': new_product_id,
                        'name': sku.name,
                        'now_price': sku.now_price,
                        'vip_price': sku.vip_price,
                        'duration': sku.duration,
                        'type_price_unit': sku.type_price_unit,
                        'define_commission': sku.define_commission,
                        'commission_type': sku.commission_type
                    }

                    sku_insert_query = text("""
                        INSERT INTO product_sku (
                            productid, name, now_price, vip_price, duration,
                            type_price_unit, define_commission, commission_type
                        ) VALUES (
                            :productid, :name, :now_price, :vip_price, :duration,
                            :type_price_unit, :define_commission, :commission_type
                        )
                    """)
                    await query_db.execute(sku_insert_query, sku_data)
                    new_skus.append(sku_data)

            return CrudResponseModel(
                is_success=True,
                message=f'成功创建{len(new_products)}个产品和{len(new_skus)}个SKU',
                result={
                    'products_count': len(new_products),
                    'skus_count': len(new_skus),
                    'product_id_mapping': product_id_mapping
                }
            )

        except Exception as e:
            raise BusinessException(message=f'创建初始产品失败: {str(e)}')

    @classmethod
    async def _create_internal_user(
        cls,
        query_db: AsyncSession,
        company_uuid: str,
        store_uuid: str,
        store_id: int,
        company_name: str,
        store_name: str,
        register_data: ExperienceRegisterRequest
    ) -> CrudResponseModel:
        """
        创建内部用户账号
        """
        # 生成UUID
        user_uuid = CommonUtil.get_uuid_without_hyphen()

        # 生成随机密码（8位小写字母+数字）
        import random
        import string
        chars = string.ascii_lowercase + string.digits
        default_password = ''.join(random.choice(chars) for _ in range(8))
        encrypted_password = PwdUtil.get_password_hash(default_password)

        # 使用手机号作为用户名
        mobile = register_data.phone_number

        # 插入内部用户数据
        user_data = {
            'uuid': user_uuid,
            'mobile': mobile,
            'password': encrypted_password,
            'name': store_name,
            'company_id': company_uuid,
            'company_name': company_name,
            'store_id': str(store_id),
            'store_uuid': store_uuid,
            'store_name': store_name,
            'role_id': '1',
            'role_name': '超级管理员',
            'status': '1'
        }

        # 使用ORM插入内部用户数据
        from module_admin.entity.do.internal_user import InternalUser
        db_internal_user = InternalUser(**user_data)
        query_db.add(db_internal_user)
        await query_db.flush()

        # 插入内部用户权限表
        permission_data = {
            'user_id': str(db_internal_user.id),
            'action_ids': '',
            'action_menu': ''
        }

        # 使用ORM插入权限数据
        from module_admin.entity.do.internal_user_permission import InternalUserPermission
        db_permission = InternalUserPermission(**permission_data)
        query_db.add(db_permission)
        await query_db.flush()

        return CrudResponseModel(
            is_success=True,
            message=f'创建内部用户账号成功，用户名：{store_name}，默认密码：{default_password}',
            result={'username': store_name, 'password': default_password, 'mobile': mobile}
        )

    @classmethod
    async def _update_experience_status(
        cls,
        query_db: AsyncSession,
        experience_id: int,
        status: int
    ) -> CrudResponseModel:
        """
        更新商户状态
        """
        try:
            update_query = text("""
                UPDATE experience_table SET status = :status WHERE id = :id
            """)
            await query_db.execute(update_query, {'status': status, 'id': experience_id})

            return CrudResponseModel(is_success=True, message='状态更新成功')
        except Exception as e:
            raise BusinessException(message=f'更新商户状态失败: {str(e)}')

    @classmethod
    async def _create_company_version_relation(
        cls,
        query_db: AsyncSession,
        company_uuid: str
    ) -> CrudResponseModel:
        """
        创建公司软件版本关联（关联所有可用版本）
        """
        try:

            # 获取所有可用的软件版本
            version_query = text("SELECT uuid, price, name FROM software_version WHERE is_available = 1")
            version_result = await query_db.execute(version_query)
            version_rows = version_result.fetchall()

            if not version_rows:
                # 如果没有可用版本，跳过版本关联
                return CrudResponseModel(is_success=True, message='未找到可用的软件版本，跳过版本关联')

            # 批量创建版本关联数据
            relation_data_list = []
            current_time = datetime.now()
            base_order_no = f'ORDER-{current_time.strftime("%Y%m%d")}'
            
            for index, version_row in enumerate(version_rows):
                version_uuid = version_row[0]
                version_price = version_row[1]
                version_name = version_row[2]
                
                relation_data = {
                    'company_uuid': company_uuid,
                    'version_uuid': version_uuid,
                    'purchase_time': current_time,
                    'expire_time': current_time + timedelta(days=7),  # 7天试用期
                    'status': 1,
                    'price': version_price,
                    'order_no': f'{base_order_no}-{CommonUtil.get_compact_id(length=8)}',
                    'create_by': 'admin',
                    'update_by': 'admin',
                    'remark': f'关联版本: {version_name}'
                }
                relation_data_list.append(relation_data)

            # 批量插入版本关联
            relation_insert_query = text("""
                INSERT INTO company_version_relation (
                    company_uuid, version_uuid, purchase_time, expire_time, status,
                    price, order_no, create_by, update_by, remark
                ) VALUES (
                    :company_uuid, :version_uuid, :purchase_time, :expire_time, :status,
                    :price, :order_no, :create_by, :update_by, :remark
                )
            """)
            
            for relation_data in relation_data_list:
                await query_db.execute(relation_insert_query, relation_data)

            return CrudResponseModel(
                is_success=True, 
                message=f'创建软件版本关联成功，共关联{len(relation_data_list)}个版本'
            )

        except Exception as e:
            raise BusinessException(message=f'创建软件版本关联失败: {str(e)}')
